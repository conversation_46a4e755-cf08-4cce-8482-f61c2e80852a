<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PulseCraft - AI Brand Strategist</title>
    <meta name="description" content="PulseCraft is your AI Brand Strategist. It moves beyond random posts by building a deep, cohesive content plan and then automates your entire workflow." />
    <meta name="keywords" content="AI, content creation, brand strategy, social media, marketing, automation" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://closetodeploypulsecraft-o4pfxu6o2-the-impact-makers-projects.vercel.app/" />
    <meta property="og:title" content="PulseCraft - AI Brand Strategist" />
    <meta property="og:description" content="Stop Guessing. Start Strategizing. PulseCraft is your AI Brand Strategist for cohesive, automated content creation." />
    <meta property="og:image" content="https://closetodeploypulsecraft-o4pfxu6o2-the-impact-makers-projects.vercel.app/og-image.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://closetodeploypulsecraft-o4pfxu6o2-the-impact-makers-projects.vercel.app/" />
    <meta property="twitter:title" content="PulseCraft - AI Brand Strategist" />
    <meta property="twitter:description" content="Stop Guessing. Start Strategizing. PulseCraft is your AI Brand Strategist for cohesive, automated content creation." />
    <meta property="twitter:image" content="https://closetodeploypulsecraft-o4pfxu6o2-the-impact-makers-projects.vercel.app/og-image.png" />

    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Space+Grotesk:wght@700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        :root {
            --background: #f8fafc; /* gray-50 */
            --foreground: #0f172a; /* slate-900 */
            --card: #ffffff;
            --card-foreground: #0f172a;
            --popover: #ffffff;
            --popover-foreground: #0f172a;
            --primary: #9333ea; /* purple-600 */
            --primary-foreground: #ffffff;
            --secondary: #d946ef; /* fuchsia-500 */
            --secondary-foreground: #ffffff;
            --accent: #a855f7; /* purple-500 */
            --accent-foreground: #ffffff;
            --destructive: #ef4444; /* red-500 */
            --destructive-foreground: #ffffff;
            --muted: #f1f5f9; /* slate-100 */
            --muted-foreground: #64748b; /* slate-500 */
            --border: #e2e8f0; /* slate-200 */
            --input: #e2e8f0;
            --ring: #9333ea;
            --radius: 0.5rem;
            
            /* CSS variables for onboarding tour */
            --highlight-x: 50%;
            --highlight-y: 50%;
            --highlight-w: 0px;
            --highlight-h: 0px;
            --highlight-r: 0px;
        }

        html, body, #root {
            height: 100%;
        }

        body { 
            font-family: 'Inter', sans-serif; 
            background-color: var(--muted);
            color: var(--foreground);
        }
        .modal-content::-webkit-scrollbar { width: 8px; }
        .modal-content::-webkit-scrollbar-track { background: var(--muted); }
        .modal-content::-webkit-scrollbar-thumb { background: #94a3b8; border-radius: 4px; }
        .modal-content::-webkit-scrollbar-thumb:hover { background: #64748b; }

        /* Custom Prose Styles for Generated Content */
        .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 { 
            color: var(--foreground);
            font-weight: 700; 
            margin-top: 1.5em; 
            margin-bottom: 0.6em; 
            line-height: 1.2;
        }
        .prose h1 { font-size: 2rem; }
        .prose h2 { font-size: 1.75rem; border-bottom: 1px solid var(--border); padding-bottom: 0.3em; }
        .prose h3 { font-size: 1.5rem; }
        .prose h4 { font-size: 1.25rem; }
        .prose h5 { font-size: 1.1rem; }
        .prose strong { color: var(--foreground); }
        .prose a { color: var(--primary); text-decoration: none; }
        .prose a:hover { text-decoration: underline; }
        .prose ul { list-style-position: outside; padding-left: 1.5em; }
        .prose ul > li::marker { color: var(--muted-foreground); }
        .prose p, .prose li { color: #334155; margin-bottom: 0.75em; line-height: 1.6; }
        .prose blockquote { border-left-color: var(--primary); }
        
        /* Mouse Spotlight Effect */
        body::after {
            content: '';
            position: fixed;
            width: 400px;
            height: 400px;
            top: var(--mouse-y, -1000px);
            left: var(--mouse-x, -1000px);
            transform: translate(-50%, -50%);
            background: radial-gradient(
                circle,
                rgba(147, 51, 234, 0.06) 0%, /* purple-600 */
                rgba(217, 70, 239, 0.04) 50%, /* fuchsia-500 */
                transparent 70%
            );
            filter: blur(80px);
            pointer-events: none;
            z-index: 10;
            opacity: 0;
            transition: opacity 0.4s ease-out;
        }

        body:hover::after {
            opacity: 1;
        }

        /* Animation for pulsing feedback button */
        @keyframes pulse-glow {
            0%, 100% {
                box-shadow: 0 0 0 0 rgba(147, 51, 234, 0.4);
            }
            50% {
                box-shadow: 0 0 0 8px rgba(147, 51, 234, 0);
            }
        }
        .animate-pulse-glow {
            animation: pulse-glow 2.5s infinite;
        }
        
        /* Animation for "thinking" input field */
        @keyframes thinking-glow {
          0% {
            box-shadow: 0 0 5px rgba(147, 51, 234, 0.2), inset 0 0 5px rgba(147, 51, 234, 0.1);
            border-color: rgba(147, 51, 234, 0.4);
          }
          50% {
            box-shadow: 0 0 15px rgba(147, 51, 234, 0.6), inset 0 0 10px rgba(147, 51, 234, 0.3);
            border-color: rgba(147, 51, 234, 0.8);
          }
          100% {
            box-shadow: 0 0 5px rgba(147, 51, 234, 0.2), inset 0 0 5px rgba(147, 51, 234, 0.1);
            border-color: rgba(147, 51, 234, 0.4);
          }
        }
        .thinking-glow {
          animation: thinking-glow 1.5s infinite ease-in-out;
        }

        /* Onboarding Tour Spotlight */
        .onboarding-overlay {
            position: fixed;
            inset: 0;
            z-index: 9998;
            background-color: transparent;
            box-shadow: 0 0 0 100vmax rgba(15, 23, 42, 0.7);
            clip-path: inset(
                calc(var(--highlight-y) - var(--highlight-r))
                calc(100vw - (var(--highlight-x) + var(--highlight-w) + var(--highlight-r)))
                calc(100vh - (var(--highlight-y) + var(--highlight-h) + var(--highlight-r)))
                calc(var(--highlight-x) - var(--highlight-r))
                round var(--highlight-r)
            );
            transition: clip-path 0.5s cubic-bezier(0.25, 1, 0.5, 1);
            pointer-events: none;
        }

    </style>
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "@supabase/supabase-js": "https://esm.sh/@supabase/supabase-js@^2.52.0",
    "react/": "https://esm.sh/react@^19.1.0/",
    "jszip": "https://esm.sh/jszip@^3.10.1",
    "@google/genai": "https://esm.sh/@google/genai@^1.9.0",
    "@stripe/stripe-js": "https://esm.sh/@stripe/stripe-js@^7.5.0",
    "@stripe/react-stripe-js": "https://esm.sh/@stripe/react-stripe-js@^3.7.0"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
</head>
<body class="antialiased">
    <div id="root"></div>
<script type="module" src="/index.tsx"></script>
</body>
</html>
