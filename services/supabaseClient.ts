import { createClient } from '@supabase/supabase-js';

// Provide placeholder values if the environment variables are not set.
// This prevents the application from crashing, allowing it to load.
// Authentication will not work with these placeholders, but the app will be usable for UI review.
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || '';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';

if (!supabaseUrl || !supabaseAnonKey) {
    console.warn(
        'Supabase environment variables (VITE_SUPABASE_URL, VITE_SUPABASE_ANON_KEY) are not set. ' +
        'Using placeholder values. Authentication will fail until they are configured.'
    );
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);