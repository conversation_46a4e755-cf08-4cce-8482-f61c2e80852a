/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import { GoogleGenAI, Type, GenerateContentResponse, Chat } from "@google/genai";
import { Settings, Post, GeneratedPostContent, Platform, WeekData, PostIdea, PostVariation, PlanDuration } from '../types';
import { frameworkDescriptions } from "../strategyFrameworks";

const API_KEY = import.meta.env.VITE_API_KEY || 'API_KEY_placeholder';

if (API_KEY === 'API_KEY_placeholder') {
    console.warn(
        'Gemini API Key (API_KEY) is not set in your environment configuration. ' +
        'Using a placeholder. AI features will not work until the key is provided.'
    );
}

const ai = new GoogleGenAI({ apiKey: API_KEY });

const generatedPostContentSchema = {
    type: Type.OBJECT,
    properties: {
        caption: { type: Type.STRING, description: "The main text content for the social media post." },
        hashtags: { type: Type.STRING, description: "A space-separated list of relevant hashtags, including the # symbol." },
        image_prompt: { type: Type.STRING, description: "A detailed, descriptive prompt for an AI image generator to create a visual for this post." },
    },
    required: ["caption", "hashtags", "image_prompt"],
};

export const generateImage = async (prompt: string, settings: Settings, count: number = 1, styleOverride?: string): Promise<string[] | null> => {
    const finalStyle = styleOverride || settings.styleDescription;
    const fullPrompt = `${prompt}, in the visual style of: ${finalStyle}. For the brand '${settings.brandName}'.`;
    const cacheKey = `img_cache_${fullPrompt.replace(/\s/g, '_')}`;

    // 1) Try cache first
    try {
        const cachedImages = localStorage.getItem(cacheKey);
        if (cachedImages) {
            const images = JSON.parse(cachedImages) as string[];
            if (images.length >= count) {
                console.log("Returning cached images for prompt:", fullPrompt);
                return images.slice(0, count);
            }
        }
    } catch (e) {
        console.warn("Could not read from image cache", e);
    }

    // Helpers
    const saveCache = (urls: string[]) => {
        try {
            localStorage.setItem(cacheKey, JSON.stringify(urls));
        } catch (e) {
            console.warn("Could not save images to cache", e);
        }
    };

    const blobToDataURL = (blob: Blob): Promise<string> =>
        new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onloadend = () => resolve(reader.result as string);
            reader.onerror = reject;
            reader.readAsDataURL(blob);
        });

    // Provider 1: Google Imagen (primary)
    const tryGoogleImagen = async (): Promise<string[] | null> => {
        try {
            const response = await ai.models.generateImages({
                model: 'imagen-3.0-generate-002',
                prompt: fullPrompt,
                config: {
                    numberOfImages: count,
                    outputMimeType: 'image/jpeg',
                    aspectRatio: '1:1',
                },
            });

            if (response.generatedImages && response.generatedImages.length > 0) {
                const imageUrls = response.generatedImages.map(img => `data:image/jpeg;base64,${img.image?.imageBytes || ''}`);
                saveCache(imageUrls);
                return imageUrls.slice(0, count);
            }
            return null;
        } catch (error) {
            console.warn('Primary provider (Imagen) failed:', error);
            return null;
        }
    };

    // Provider 2: Pollinations (free, no key)
    const tryPollinations = async (): Promise<string[] | null> => {
        try {
            const urls: string[] = [];
            for (let i = 0; i < count; i++) {
                const seed = Math.floor(Math.random() * 1e9);
                const url = `https://image.pollinations.ai/prompt/${encodeURIComponent(fullPrompt)}?width=1024&height=1024&nologo=true&seed=${seed}`;
                const res = await fetch(url, { mode: 'cors' });
                if (!res.ok) throw new Error(`Pollinations fetch failed with status ${res.status}`);
                const blob = await res.blob();
                const dataUrl = await blobToDataURL(blob);
                urls.push(dataUrl);
            }
            saveCache(urls);
            return urls;
        } catch (error) {
            console.warn('Fallback provider (Pollinations) failed:', error);
            return null;
        }
    };

    // Provider 3: Lexica search (free image search -> fetch to dataURL)
    const tryLexica = async (): Promise<string[] | null> => {
        try {
            const res = await fetch(`https://lexica.art/api/v1/search?q=${encodeURIComponent(fullPrompt)}`, { mode: 'cors' });
            if (!res.ok) throw new Error(`Lexica search failed with status ${res.status}`);
            const json = await res.json();
            const images = (json?.images || []) as any[];
            if (!images.length) return null;

            const list: string[] = [];
            for (let i = 0; i < Math.min(count, images.length); i++) {
                const candidate =
                    images[i]?.src ||
                    images[i]?.image ||
                    images[i]?.url ||
                    images[i]?.urls?.small ||
                    images[i]?.urls?.image;
                if (!candidate) continue;
                const imgRes = await fetch(candidate, { mode: 'cors' });
                if (!imgRes.ok) continue;
                const blob = await imgRes.blob();
                const dataUrl = await blobToDataURL(blob);
                list.push(dataUrl);
            }
            if (list.length) {
                saveCache(list);
                return list;
            }
            return null;
        } catch (error) {
            console.warn('Fallback provider (Lexica) failed:', error);
            return null;
        }
    };

    // Final fallback: Placeholder (SVG data URL) - ensures graceful UI
    const placeholderDataUrl = (brand: string) => {
        const text = (brand || 'PulseCraft').slice(0, 20);
        const svg = `
<svg xmlns='http://www.w3.org/2000/svg' width='1024' height='1024'>
  <defs>
    <linearGradient id='g' x1='0' x2='1' y1='0' y2='1'>
      <stop offset='0%' stop-color='#e2e8f0'/>
      <stop offset='100%' stop-color='#cbd5e1'/>
    </linearGradient>
  </defs>
  <rect width='1024' height='1024' fill='url(#g)'/>
  <g fill='#64748b' font-family='system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif'>
    <text x='50%' y='48%' text-anchor='middle' font-size='48' font-weight='700'>No Image</text>
    <text x='50%' y='56%' text-anchor='middle' font-size='28'>${text}</text>
  </g>
</svg>`;
        return `data:image/svg+xml;utf8,${encodeURIComponent(svg)}`;
    };

    // Attempt providers in configured order
    const orderEnv = (import.meta.env as any).VITE_IMAGE_PROVIDER_ORDER as string | undefined;
    const providers = (orderEnv || 'google,pollinations,lexica')
        .split(',')
        .map(s => s.trim().toLowerCase())
        .filter(Boolean);

    let result: string[] | null = null;
    for (const p of providers) {
        if (p === 'google' || p === 'imagen' || p === 'google-imagen') {
            result = await tryGoogleImagen();
        } else if (p === 'pollinations') {
            result = await tryPollinations();
        } else if (p === 'lexica') {
            result = await tryLexica();
        } else {
            console.warn('Unknown image provider in VITE_IMAGE_PROVIDER_ORDER:', p);
            continue;
        }
        if (result && result.length) {
            return result.slice(0, count);
        }
    }

    // No providers worked - return silent placeholders to avoid scaring users with errors
    const placeholders = Array.from({ length: count }, () => placeholderDataUrl(settings.brandName || ''));
    saveCache(placeholders);
    return placeholders;
};

const getBaseSystemInstruction = (settings: Settings): string => {
    const langMap = { 'en': 'English', 'fr': 'French', 'es': 'Spanish' };
    const language = langMap[settings.generationLanguage] || 'English';
    let instruction = `You are a world-class digital marketing strategist and content creator named 'Co-pilot'. You are an expert in the "${settings.strategyFramework}". Your entire response must be in ${language}. You are generating a plan for the brand '${settings.brandName}'.`;
    if (settings.characterDescription) {
        instruction += ` You should adopt the following persona: ${settings.characterDescription}`;
    }
    return instruction;
};

export const generateContentPlan = async (settings: Settings): Promise<string> => {
    const langMap = { 'en': 'English', 'fr': 'French', 'es': 'Spanish' };
    const language = langMap[settings.generationLanguage] || 'English';
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonthName = now.toLocaleString(settings.generationLanguage, { month: 'long' });

    const durationMap: Record<PlanDuration, string> = {
        '6m': '6-month',
        '1y': '1-year',
        '1.5y': '1.5-year',
        '2y': '2-year',
    };
    const planDurationText = durationMap[settings.planDuration] || '2-year';
    const frameworkInfo = frameworkDescriptions[settings.strategyFramework] || { title: 'Custom Framework', approach: 'User-defined approach', bestFor: 'Specific needs as defined by the user' };

    const prompt = `Based on the following business profile, generate a comprehensive, actionable ${planDurationText} content strategy.

**Business Profile:**
- **Brand Name:** ${settings.brandName || ''}
- **Website:** ${settings.websiteUrl || ''}
- **Profile Bio:** ${settings.bio || ''}
- **Business Context:** ${settings.businessContext || ''}
- **Target Platforms:** ${settings.platforms.join(', ')}
- **Strategic Framework:** ${settings.strategyFramework || ''}
  - **Framework Philosophy:** ${frameworkInfo.title} - ${frameworkInfo.approach}
  - **Best For:** ${frameworkInfo.bestFor}
- **Visual Style:** ${settings.styleDescription || ''}

**Instructions:**
1.  **Structure:** Organize the plan into the requested time duration. Start the plan from **${currentMonthName} ${currentYear}**. The year and month names should be explicit and in ${language} (e.g., "## ${currentYear}", "#### ${currentMonthName}: ...").
2.  **Format:** Use Markdown. Start with a "# Brand Voice & Strategic Positioning Guide" section. Then, use \`## Year\`, \`### Quarter\`, \`#### Month\`, \`##### Week\`.
3.  **Weekly Details:** For each week, provide details using these exact English keys, followed by two asterisks, a colon, and a space (e.g., \`**Strategy:** ...\`):
    - **Strategy:** A one-sentence strategic goal for the week.
    - **Audience Segment:** The target audience for this week's content.
    - **Story Idea:** A compelling narrative for ephemeral content (e.g., Instagram Stories).
    - **Community Engagement:** A specific question or call-to-action to engage the community.
    - **Post Ideas:** Provide 2-3 specific post ideas. For each idea, provide:
        - **Core Concept:** The main idea of the post.
        - **Format:** The suggested format (e.g., Carousel, Reel, Static Image, Text Post).
4.  **Language:** The content for the plan MUST be in ${language}.
5.  **Completeness Mandate:** You MUST provide all details for EVERY single week of the plan. DO NOT omit any weekly details. Specifically, you must provide the 'Strategy', 'Audience Segment', 'Story Idea', 'Community Engagement', and at least TWO 'Post Ideas' (each with a 'Core Concept' and 'Format') for every week. An incomplete plan is not acceptable.`;
    
    const response = await ai.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: prompt,
        config: { systemInstruction: getBaseSystemInstruction(settings) },
    });
    return (response.text ?? '');
};

export async function* generateContentPlanStream(settings: Settings): AsyncGenerator<string> {
    const langMap = { 'en': 'English', 'fr': 'French', 'es': 'Spanish' };
    const language = langMap[settings.generationLanguage] || 'English';
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonthName = now.toLocaleString(settings.generationLanguage, { month: 'long' });

    const durationMap: Record<PlanDuration, string> = {
        '6m': '6-month',
        '1y': '1-year',
        '1.5y': '1.5-year',
        '2y': '2-year',
    };
    const planDurationText = durationMap[settings.planDuration] || '2-year';
    const frameworkInfo = frameworkDescriptions[settings.strategyFramework] || { title: 'Custom Framework', approach: 'User-defined approach', bestFor: 'Specific needs as defined by the user' };

    const prompt = `Based on the following business profile, generate a comprehensive, actionable ${planDurationText} content strategy.

**Business Profile:**
- **Brand Name:** ${settings.brandName || ''}
- **Website:** ${settings.websiteUrl || ''}
- **Profile Bio:** ${settings.bio || ''}
- **Business Context:** ${settings.businessContext || ''}
- **Target Platforms:** ${settings.platforms.join(', ')}
- **Strategic Framework:** ${settings.strategyFramework || ''}
  - **Framework Philosophy:** ${frameworkInfo.title} - ${frameworkInfo.approach}
  - **Best For:** ${frameworkInfo.bestFor}
- **Visual Style:** ${settings.styleDescription || ''}

**Instructions:**
1.  **Structure:** Organize the plan into the requested time duration. Start the plan from **${currentMonthName} ${currentYear}**. The year and month names should be explicit and in ${language} (e.g., "## ${currentYear}", "#### ${currentMonthName}: ...").
2.  **Format:** Use Markdown. Start with a "# Brand Voice & Strategic Positioning Guide" section. Then, use \`## Year\`, \`### Quarter\`, \`#### Month\`, \`##### Week\`.
3.  **Weekly Details:** For each week, provide details using these exact English keys, followed by two asterisks, a colon, and a space (e.g., \`**Strategy:** ...\`):
    - **Strategy:** A one-sentence strategic goal for the week.
    - **Audience Segment:** The target audience for this week's content.
    - **Story Idea:** A compelling narrative for ephemeral content (e.g., Instagram Stories).
    - **Community Engagement:** A specific question or call-to-action to engage the community.
    - **Post Ideas:** Provide 2-3 specific post ideas. For each idea, provide:
        - **Core Concept:** The main idea of the post.
        - **Format:** The suggested format (e.g., Carousel, Reel, Static Image, Text Post).
4.  **Language:** The content for the plan MUST be in ${language}.
5.  **Completeness Mandate:** You MUST provide all details for EVERY single week of the plan. DO NOT omit any weekly details. Specifically, you must provide the 'Strategy', 'Audience Segment', 'Story Idea', 'Community Engagement', and at least TWO 'Post Ideas' (each with a 'Core Concept' and 'Format') for every week. An incomplete plan is not acceptable.`;
    
    const response = await ai.models.generateContentStream({
        model: 'gemini-2.5-flash',
        contents: prompt,
        config: { systemInstruction: getBaseSystemInstruction(settings) },
    });

    for await (const chunk of response) {
        yield (chunk.text ?? '');
    }
}

export const refineContentPlan = async (settings: Settings, existingPlan: string, refinementRequest: string): Promise<{newPlan: string, diff: string} | null> => {
    const prompt = `A user wants to refine their existing content plan.
    
    **USER REQUEST:** "${refinementRequest}"

    **EXISTING FULL PLAN:**
    """
    ${existingPlan}
    """

    **Instructions:**
    1.  Rewrite the entire plan incorporating the user's request. Maintain the original markdown structure exactly.
    2.  Create a "diff" of the changes in a separate markdown block. Summarize the key changes you made. Use a format like "- REMOVED: [old text] \n+ ADDED: [new text]".
    3.  Return a single JSON object with two keys: "newPlan" (containing the full rewritten plan as a markdown string) and "diff" (containing the summary of changes as a markdown string).`;

    const response = await ai.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: prompt,
        config: {
            systemInstruction: getBaseSystemInstruction(settings),
            responseMimeType: "application/json",
            responseSchema: {
                type: Type.OBJECT,
                properties: {
                    newPlan: { type: Type.STRING, description: "The full, rewritten markdown content plan." },
                    diff: { type: Type.STRING, description: "A markdown summary of the changes made." },
                },
                required: ["newPlan", "diff"],
            },
        }
    });
    
    try {
        const json = JSON.parse((response.text ?? ''));
        return json as {newPlan: string, diff: string};
    } catch(e) {
        console.error("Failed to parse plan refinement response:", response.text);
        return null;
    }
};

export const improvePost = async (post: Post, settings: Settings, request: string): Promise<GeneratedPostContent | null> => {
    const prompt = `A user wants to improve a social media post.
    
    **USER REQUEST:** "${request}"

    **ORIGINAL POST:**
    - **Platform:** ${post.platform}
    - **Caption:** ${post.caption}
    - **Hashtags:** ${post.hashtags}
    - **Image Prompt:** ${post.image_prompt}

    **BRAND INFO:**
    - **Brand Name:** ${settings.brandName || ''}
    - **Brand Character/Tone:** ${settings.characterDescription || ''}
    - **Business Context:** ${settings.businessContext || ''}

    Based on the user's request, provide an improved version of the post in JSON format. If the request implies a visual change, update the image prompt accordingly.`;

    const response = await ai.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: prompt,
        config: {
            responseMimeType: "application/json",
            responseSchema: generatedPostContentSchema,
        }
    });

    const json = JSON.parse((response.text ?? ''));
    return json as GeneratedPostContent;
};

export const adaptPostForPlatform = async (post: Post, settings: Settings, targetPlatform: Platform): Promise<GeneratedPostContent | null> => {
    const prompt = `Adapt the following social media post from '${post.platform}' to be optimized for '${targetPlatform}'. Adjust the tone, length, formatting, and hashtags as needed for the new platform.

    **ORIGINAL POST:**
    - **Caption:** ${post.caption}
    - **Hashtags:** ${post.hashtags}
    - **Image Prompt:** ${post.image_prompt}

    **BRAND INFO:**
    - **Brand Name:** ${settings.brandName || ''}
    - **Brand Character/Tone:** ${settings.characterDescription || ''}
    - **Business Context:** ${settings.businessContext || ''}

    Provide the adapted post in the specified JSON format.`;
    
    const response = await ai.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: prompt,
        config: {
            responseMimeType: "application/json",
            responseSchema: generatedPostContentSchema,
        }
    });

    const json = JSON.parse((response.text ?? ''));
    return json as GeneratedPostContent;
};


export const generatePostVariations = async (settings: Settings, weekData: WeekData, idea: PostIdea, instructions?: string): Promise<PostVariation[] | null> => {
    let prompt = `Generate social media post variations for multiple platforms based on a weekly strategy and a core concept.

    **BRAND INFO:**
    - **Brand Name:** ${settings.brandName || ''}
    - **Target Platforms:** ${settings.platforms.join(', ')}
    - **Brand Character/Tone:** ${settings.characterDescription || ''}
    - **Business Context:** ${settings.businessContext || ''}

    **THIS WEEK'S STRATEGY:**
    - **Goal:** ${weekData.strategy || ''}
    - **Audience:** ${weekData.audienceSegment || ''}

    **POST IDEA:**
    - **Core Concept:** ${idea.concept}
    - **Format:** ${idea.format}`;

    if (instructions) {
        prompt += `\n\n**USER INSTRUCTIONS FOR THIS BATCH:** ${instructions}`;
    }

    prompt += `\n\nGenerate one post for each target platform listed above. Provide the output as a JSON array.`;

    const response = await ai.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: prompt,
        config: {
            responseMimeType: "application/json",
            responseSchema: {
                type: Type.ARRAY,
                items: {
                    type: Type.OBJECT,
                    properties: {
                        platform: { type: Type.STRING, description: `The target platform. Must be one of: ${settings.platforms.join(', ')}` },
                        ...generatedPostContentSchema.properties,
                    },
                    required: ["platform", "caption", "hashtags", "image_prompt"],
                }
            }
        }
    });
    
    const json = JSON.parse((response.text ?? ''));
    return json as PostVariation[];
};

export const askIhab = async (chat: Chat, question: string): Promise<GenerateContentResponse> => {
    const response = await chat.sendMessage({ message: question });
    return response;
};

export const createIhabChat = (documentation: string): Chat => {
    const systemInstruction = `You are Ihab, a friendly and helpful AI assistant for the web application PulseCraft. Your goal is to answer user questions about how to use the app.
    - Your knowledge is STRICTLY limited to the documentation provided below. DO NOT answer questions outside of this context.
    - If a user asks something you don't know, you must say "I'm sorry, I don't have information about that. I can only help with questions about PulseCraft."
    - Keep your answers concise and easy to understand.
    - Format your answers with Markdown for readability (bolding, lists, etc.).
    - When you answer, refer to the documentation as your source of truth.
    
    ---DOCUMENTATION KNOWLEDGE BASE---
    ${documentation}
    ---END OF DOCUMENTATION---
    `;

    const chat: Chat = ai.chats.create({
        model: 'gemini-2.5-flash',
        config: { systemInstruction },
    });
    return chat;
};

export type { PostVariation, Chat };
