import { supabase } from './supabaseClient';

const DEFAULT_BUCKET = (import.meta as any).env?.VITE_SUPABASE_BUCKET_IMAGES || 'generated-images';

function dataUrlToBlob(dataUrl: string): { blob: Blob; contentType: string } {
  const parts = dataUrl.split(',');
  if (parts.length < 2) throw new Error('Invalid data URL');
  const meta = parts[0]; // data:image/jpeg;base64
  const base64 = parts[1];
  const match = /data:(.*?);base64/.exec(meta);
  const contentType = match?.[1] || 'image/jpeg';
  const byteCharacters = atob(base64);
  const byteNumbers = new Array(byteCharacters.length);
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  const byteArray = new Uint8Array(byteNumbers);
  const blob = new Blob([byteArray], { type: contentType });
  return { blob, contentType };
}

export function isHttpUrl(url?: string | null): boolean {
  if (!url) return false;
  return /^https?:\/\//i.test(url);
}

/**
 * Uploads a data URL to Supabase Storage and returns a public URL.
 * Returns null if upload fails (caller can fallback to using the data URL).
 */
export async function saveImageDataUrlToSupabase(
  dataUrl: string,
  options?: {
    userId?: string | null;
    postId?: number | string;
    bucket?: string;
    filenameHint?: string;
  }
): Promise<string | null> {
  try {
    const { blob, contentType } = dataUrlToBlob(dataUrl);
    const bucket = options?.bucket || DEFAULT_BUCKET;
    const uid = (options?.userId || 'anon').replace(/[^a-zA-Z0-9_-]/g, '');
    const pid = String(options?.postId ?? 'nopost').replace(/[^a-zA-Z0-9_-]/g, '');
    const timestamp = Date.now();
    const random = Math.random().toString(36).slice(2);
    const ext = contentType.includes('png') ? 'png' : contentType.includes('webp') ? 'webp' : 'jpg';
    const baseName = (options?.filenameHint || 'generated').replace(/[^a-zA-Z0-9_-]/g, '');
    const path = `${uid}/${pid}/${baseName}-${timestamp}-${random}.${ext}`;

    const { error: uploadError } = await supabase.storage.from(bucket).upload(path, blob, {
      contentType,
      upsert: true
    });
    if (uploadError) {
      console.warn('Supabase upload failed:', uploadError.message);
      return null;
    }

    const { data } = supabase.storage.from(bucket).getPublicUrl(path);
    return data.publicUrl || null;
  } catch (e: any) {
    console.warn('Failed to upload image to Supabase:', e?.message || e);
    return null;
  }
}