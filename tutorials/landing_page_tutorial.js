(async () => {
    // --- Constants ---
    const TUTORIAL_STYLES_ID = 'tutorial-styles';
    const OVERLAY_ID = 'tutorial-overlay';
    const HIGHLIGHT_ID = 'tutorial-highlight';
    const ANNOTATION_ID = 'tutorial-annotation';
    const TYPING_TEXT_ID = 'typing-text';

    const TYPING_SPEED_MS = 30;
    const INPUT_TYPING_SPEED_MS = 50;
    const STEP_PAUSE_MS = 1000;
    const ANNOTATION_DISPLAY_TIME_MS = 3000;
    const INITIAL_ANNOTATION_DISPLAY_TIME_MS = 4000;
    const ELEMENT_WAIT_TIMEOUT_MS = 10000;

    const tutorialStyles = `
        .tutorial-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(15, 23, 42, 0.23); /* Darker, but less opaque than pure black */
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
            pointer-events: none; /* Allow clicks to pass through by default */
        }
        .tutorial-highlight {
            position: absolute;
            border: 3px solid #8a2be2; /* BlueViolet */
            border-radius: 8px;
            z-index: 10000;
            transition: all 0.5s ease-in-out;
            pointer-events: none;
            box-shadow: 0 0 15px rgba(138, 43, 226, 0.8);
        }
        .tutorial-annotation {
            position: absolute;
            background: #333;
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Inter', sans-serif;
            font-size: 16px;
            max-width: 350px;
            text-align: left;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            z-index: 10001;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.3s ease-out, transform 0.3s ease-out;
            pointer-events: none;
        }
        .tutorial-annotation.show {
            opacity: 1;
            transform: translateY(0);
        }
        .tutorial-annotation::before {
            content: '';
            position: absolute;
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-bottom: 10px solid #333;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
        }
        .tutorial-button-overlay {
            position: absolute;
            background: rgba(138, 43, 226, 0.3); /* BlueViolet with transparency */
            border-radius: 8px;
            z-index: 10002;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        .tutorial-button-overlay:hover {
            background: rgba(138, 43, 226, 0.5);
        }
        .tutorial-typing-cursor {
            display: inline-block;
            width: 2px;
            height: 1em;
            background-color: white;
            animation: blink-caret 0.75s step-end infinite;
            vertical-align: middle;
            margin-left: 2px;
        }
        @keyframes blink-caret {
            from, to { background-color: transparent }
            50% { background-color: white; }
        }
    `;

    // --- DOM Manipulation Functions ---
    const addStyles = () => {
        const styleElement = document.createElement('style');
        styleElement.id = TUTORIAL_STYLES_ID;
        styleElement.innerHTML = tutorialStyles;
        document.head.appendChild(styleElement);
    };

    const removeStyles = () => {
        const styleElement = document.getElementById(TUTORIAL_STYLES_ID);
        if (styleElement) {
            styleElement.remove();
        }
    };

    const createOverlayElements = () => {
        const overlay = document.createElement('div');
        overlay.className = 'tutorial-overlay';
        overlay.id = OVERLAY_ID;
        document.body.appendChild(overlay);

        const highlight = document.createElement('div');
        highlight.className = 'tutorial-highlight';
        highlight.id = HIGHLIGHT_ID;
        document.body.appendChild(highlight);

        const annotation = document.createElement('div');
        annotation.className = 'tutorial-annotation';
        annotation.id = ANNOTATION_ID;
        document.body.appendChild(annotation);
    };

    const removeOverlayElements = () => {
        const overlay = document.getElementById(OVERLAY_ID);
        const highlight = document.getElementById(HIGHLIGHT_ID);
        const annotation = document.getElementById(ANNOTATION_ID);
        if (overlay) overlay.remove();
        if (highlight) highlight.remove();
        if (annotation) annotation.remove();
    };

    const highlightElement = (element, offset = { x: 0, y: 0, width: 0, height: 0 }) => {
        const highlight = document.getElementById(HIGHLIGHT_ID);
        if (!highlight || !element) return;

        const rect = element.getBoundingClientRect();
        highlight.style.left = `${rect.left + window.scrollX + offset.x}px`;
        highlight.style.top = `${rect.top + window.scrollY + offset.y}px`;
        highlight.style.width = `${rect.width + offset.width}px`;
        highlight.style.height = `${rect.height + offset.height}px`;
        highlight.style.opacity = '1';
    };

    const showAnnotation = async (text, element, position = 'top') => {
        const annotation = document.getElementById(ANNOTATION_ID);
        if (!annotation || !element) return;

        annotation.innerHTML = `<span id="${TYPING_TEXT_ID}"></span><span class="tutorial-typing-cursor"></span>`;
        annotation.classList.add('show');

        // Temporarily show annotation to calculate its dimensions
        annotation.style.opacity = '1';
        annotation.style.transform = 'translateY(0)';
        // Position off-screen to avoid flicker while calculating
        annotation.style.left = '-9999px';
        annotation.style.top = '-9999px';

        const rect = element.getBoundingClientRect();
        const annotationRect = annotation.getBoundingClientRect();

        let top, left;

        if (position === 'top') {
            top = rect.top + window.scrollY - annotationRect.height - 30;
            left = rect.left + window.scrollX + rect.width / 2 - annotationRect.width / 2;
        } else if (position === 'bottom') {
            top = rect.bottom + window.scrollY + 30;
            left = rect.left + window.scrollX + rect.width / 2 - annotationRect.width / 2;
        } else if (position === 'left') {
            top = rect.top + window.scrollY + rect.height / 2 - annotationRect.height / 2;
            left = rect.left + window.scrollX - annotationRect.width - 30;
        } else if (position === 'right') {
            top = rect.top + window.scrollY + rect.height / 2 - annotationRect.height / 2;
            left = rect.right + window.scrollX + 30;
        }

        annotation.style.top = `${top}px`;
        annotation.style.left = `${left}px`;

        await typeText(text);
    };

    const hideAnnotation = () => {
        const annotation = document.getElementById(ANNOTATION_ID);
        if (annotation) {
            annotation.classList.remove('show');
        }
    };

    // --- Utility Functions ---
    const typeText = async (text) => {
        const typingTextElement = document.getElementById(TYPING_TEXT_ID);
        if (!typingTextElement) return;

        typingTextElement.textContent = '';
        for (let i = 0; i < text.length; i++) {
            typingTextElement.textContent += text.charAt(i);
            await new Promise(resolve => setTimeout(resolve, TYPING_SPEED_MS));
        }
    };

    const waitForElement = (selectors, timeout = ELEMENT_WAIT_TIMEOUT_MS) => {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            const checkElement = () => {
                for (const selector of selectors) {
                    const element = document.querySelector(selector);
                    if (element && element.offsetParent !== null) { // Check if element is visible
                        return resolve(element);
                    }
                }
                if (Date.now() - startTime > timeout) {
                    return reject(new Error(`Element not found or visible within timeout for selectors: ${selectors.join(', ')}`));
                }
                requestAnimationFrame(checkElement);
            };
            checkElement();
        });
    };

    const simulateClick = (element) => {
        if (element) {
            element.click();
        }
    };

    const simulateInput = async (element, value) => {
        if (element) {
            element.focus();
            element.value = ''; // Clear existing value
            for (let i = 0; i < value.length; i++) {
                element.value += value.charAt(i);
                element.dispatchEvent(new Event('input', { bubbles: true }));
                await new Promise(resolve => setTimeout(resolve, INPUT_TYPING_SPEED_MS));
            }
            element.blur();
        }
    };

    // --- Tutorial Logic ---
    let currentStep = 0;
    const tutorialSteps = [
        {
            description: "Welcome to PulseCraft! This tutorial will guide you through the key features of our landing page.",
            action: async () => {
                // Initial state, no specific element to highlight yet
            },
            voiceover: "Welcome to PulseCraft! This tutorial will guide you through the key features of our landing page."
        },
        {
            description: "First, let's look at the main authentication form. Here you can create a new account or sign in if you already have one.",
            action: async () => {
                const authForm = await waitForElement(['.w-full.max-w-md.mx-auto.bg-slate-800\\/60']);
                highlightElement(authForm, { x: -10, y: -10, width: 20, height: 20 });
                showAnnotation("This is the main authentication form. You can sign up for early access or sign in to your existing account.", authForm, 'left');
                await new Promise(resolve => setTimeout(resolve, INITIAL_ANNOTATION_DISPLAY_TIME_MS));
            },
            voiceover: "First, let's look at the main authentication form. Here you can create a new account or sign in if you already have one."
        },
        {
            description: "Enter your email address in this field.",
            action: async () => {
                const emailInput = await waitForElement(['input#email', 'input[placeholder="<EMAIL>"]']);
                highlightElement(emailInput);
                showAnnotation("Enter your email address here.", emailInput, 'bottom');
                await simulateInput(emailInput, "<EMAIL>");
                await new Promise(resolve => setTimeout(resolve, ANNOTATION_DISPLAY_TIME_MS));
            },
            voiceover: "Enter your email address in this field."
        },
        {
            description: "Next, input your password.",
            action: async () => {
                const passwordInput = await waitForElement(['input#password', 'input[placeholder="••••••••"]']);
                highlightElement(passwordInput);
                showAnnotation("Input your password here.", passwordInput, 'bottom');
                await simulateInput(passwordInput, "password123");
                await new Promise(resolve => setTimeout(resolve, ANNOTATION_DISPLAY_TIME_MS));
            },
            voiceover: "Next, input your password."
        },
        {
            description: "If you have an early access code, you can enter it here. This is optional.",
            action: async () => {
                const accessCodeInput = await waitForElement(['input#accessCode', 'input[placeholder="Optional — enter your code"]']);
                highlightElement(accessCodeInput);
                showAnnotation("Optionally, enter your early access code.", accessCodeInput, 'bottom');
                await simulateInput(accessCodeInput, "PULSECRAFT2025");
                await new Promise(resolve => setTimeout(resolve, ANNOTATION_DISPLAY_TIME_MS));
            },
            voiceover: "If you have an early access code, you can enter it here. This is optional."
        },
        {
            description: "Click 'Create Account' to sign up or 'Sign In' if you're already registered.",
            action: async () => {
                const submitButton = await waitForElement(['button[type="submit"][variant="primary"]', 'button.w-full.py-3.text-base']);
                highlightElement(submitButton);
                showAnnotation("Click here to create your account or sign in.", submitButton, 'bottom');
                // Do not actually click to avoid state changes
                await new Promise(resolve => setTimeout(resolve, ANNOTATION_DISPLAY_TIME_MS));
            },
            voiceover: "Click 'Create Account' to sign up or 'Sign In' if you're already registered."
        },
        {
            description: "If you already have an account, click 'Sign In' to switch to the sign-in form.",
            action: async () => {
                const toggleButton = await waitForElement(['#toggle-sign-in', 'button.font-semibold.text-purple-400', 'button:contains("Sign In")']);
                highlightElement(toggleButton);
                showAnnotation("Already have an account? Click 'Sign In'.", toggleButton, 'bottom');
                simulateClick(toggleButton); // Simulate click to show sign-in form
                await new Promise(resolve => setTimeout(resolve, ANNOTATION_DISPLAY_TIME_MS));
            },
            voiceover: "If you already have an account, click 'Sign In' to switch to the sign-in form."
        },
        {
            description: "Now you see the 'Sign In' form. You can switch back to 'Sign Up' anytime.",
            action: async () => {
                const toggleButton = await waitForElement(['#toggle-sign-up', 'button.font-semibold.text-purple-400', 'button:contains("Sign Up")']);
                highlightElement(toggleButton);
                showAnnotation("Don't have an account? Click 'Sign Up'.", toggleButton, 'bottom');
                await new Promise(resolve => setTimeout(resolve, ANNOTATION_DISPLAY_TIME_MS));
            },
            voiceover: "Now you see the 'Sign In' form. You can switch back to 'Sign Up' anytime."
        },
        {
            description: "Next, let's explore the 'Join the Waiting List' section.",
            action: async () => {
                hideAnnotation();
                const waitingListSection = await waitForElement(['#waiting-list-section', 'section.text-center.py-16 > .max-w-xl.mx-auto']);
                highlightElement(waitingListSection, { x: -10, y: -10, width: 20, height: 20 });
                showAnnotation("Join our waiting list to get updates on PulseCraft's public launch.", waitingListSection, 'right');
                await new Promise(resolve => setTimeout(resolve, INITIAL_ANNOTATION_DISPLAY_TIME_MS));
            },
            voiceover: "Next, let's explore the 'Join the Waiting List' section."
        },
        {
            description: "Enter your email here to join the waiting list.",
            action: async () => {
                const emailInput = await waitForElement(['#waiting-list-email', 'form.mt-8.flex.flex-col.sm\\:flex-row.gap-4 input[type="email"]', 'input[name="email"][placeholder="<EMAIL>"]']);
                highlightElement(emailInput);
                showAnnotation("Enter your email to join the waiting list.", emailInput, 'bottom');
                await simulateInput(emailInput, "<EMAIL>");
                await new Promise(resolve => setTimeout(resolve, ANNOTATION_DISPLAY_TIME_MS));
            },
            voiceover: "Enter your email here to join the waiting list."
        },
        {
            description: "Click 'Join Now' to submit your email.",
            action: async () => {
                const joinButton = await waitForElement(['#join-now-button', 'button[type="submit"][variant="secondary"]', 'button:contains("Join Now")']);
                highlightElement(joinButton);
                showAnnotation("Click 'Join Now' to submit.", joinButton, 'bottom');
                // Do not actually click
                await new Promise(resolve => setTimeout(resolve, ANNOTATION_DISPLAY_TIME_MS));
            },
            voiceover: "Click 'Join Now' to submit your email."
        },
        {
            description: "Finally, let's look at the 'From Spark to Strategy in 3 Steps' section, outlining our core process.",
            action: async () => {
                hideAnnotation();
                const stepsSection = await waitForElement(['#steps-section', 'section.section-container:nth-of-type(3)']);
                highlightElement(stepsSection, { x: -10, y: -10, width: 20, height: 20 });
                showAnnotation("Discover our simple 3-step process to transform your brand strategy.", stepsSection, 'top');
                await new Promise(resolve => setTimeout(resolve, INITIAL_ANNOTATION_DISPLAY_TIME_MS));
            },
            voiceover: "Finally, let's look at the 'From Spark to Strategy in 3 Steps' section, outlining our core process."
        },
        {
            description: "Step 1: Define Your DNA. Input your brand's context, voice, and visual style.",
            action: async () => {
                const step1 = await waitForElement(['#step-1-define-dna', '.relative.text-center:nth-child(2) h3:contains("Define Your DNA")']);
                highlightElement(step1.parentElement, { x: -10, y: -10, width: 20, height: 20 });
                showAnnotation("Step 1: Define Your DNA. Tell us who you are and who you want to reach.", step1.parentElement, 'bottom');
                await new Promise(resolve => setTimeout(resolve, ANNOTATION_DISPLAY_TIME_MS));
            },
            voiceover: "Step 1: Define Your DNA. Input your brand's context, voice, and visual style."
        },
        {
            description: "Step 2: Ignite the AI Engine. Select a strategic framework and generate a comprehensive content plan.",
            action: async () => {
                const step2 = await waitForElement(['#step-2-ignite-ai', '.relative.text-center:nth-child(3) h3:contains("Ignite the AI Engine")']);
                highlightElement(step2.parentElement, { x: -10, y: -10, width: 20, height: 20 });
                showAnnotation("Step 2: Ignite the AI Engine. Generate a comprehensive, long-term content plan.", step2.parentElement, 'bottom');
                await new Promise(resolve => setTimeout(resolve, ANNOTATION_DISPLAY_TIME_MS));
            },
            voiceover: "Step 2: Ignite the AI Engine. Select a strategic framework and generate a comprehensive content plan."
        },
        {
            description: "Step 3: Launch Your Narrative. Generate weeks of ready-to-post content.",
            action: async () => {
                const step3 = await waitForElement(['#step-3-launch-narrative', '.relative.text-center:nth-child(4) h3:contains("Launch Your Narrative")']);
                highlightElement(step3.parentElement, { x: -10, y: -10, width: 20, height: 20 });
                showAnnotation("Step 3: Launch Your Narrative. Get ready-to-post content with captions and hashtags.", step3.parentElement, 'bottom');
                await new Promise(resolve => setTimeout(resolve, ANNOTATION_DISPLAY_TIME_MS));
            },
            voiceover: "Step 3: Launch Your Narrative. Generate weeks of ready-to-post content."
        },
        {
            description: "This concludes our tour of the PulseCraft landing page. We hope you found it informative!",
            action: async () => {
                hideAnnotation();
                const highlight = document.getElementById(HIGHLIGHT_ID);
                if (highlight) highlight.style.opacity = '0';
                await new Promise(resolve => setTimeout(resolve, STEP_PAUSE_MS * 2));
            },
            voiceover: "This concludes our tour of the PulseCraft landing page. We hope you found it informative!"
        }
    ];

    // --- Event Handlers ---
    const handleKeyDown = (event) => {
        if (event.key === 'ArrowRight') {
            event.preventDefault(); // Prevent default scrolling behavior
            if (currentStep < tutorialSteps.length - 1) {
                currentStep++;
                runStep(currentStep);
            } else {
                endTutorial();
            }
        } else if (event.key === 'ArrowLeft') {
            event.preventDefault(); // Prevent default scrolling behavior
            if (currentStep > 0) {
                currentStep--;
                runStep(currentStep);
            }
        }
    };

    // --- Tutorial Flow Control ---
    const runStep = async (stepIndex) => {
        if (stepIndex < 0 || stepIndex >= tutorialSteps.length) {
            return;
        }

        const step = tutorialSteps[stepIndex];
        console.log(`Running step ${stepIndex + 1}/${tutorialSteps.length}: ${step.description}`);

        hideAnnotation();
        const highlight = document.getElementById(HIGHLIGHT_ID);
        if (highlight) highlight.style.opacity = '0';

        await step.action();
        await new Promise(resolve => setTimeout(resolve, STEP_PAUSE_MS));
    };

    const endTutorial = () => {
        removeOverlayElements();
        removeStyles();
        document.removeEventListener('keydown', handleKeyDown);
        console.log("Tutorial finished!");
    };

    const runTutorial = async () => {
        addStyles();
        createOverlayElements();
        document.addEventListener('keydown', handleKeyDown);

        await runStep(currentStep);
    };

    // Initialize the tutorial
    runTutorial();
})();
