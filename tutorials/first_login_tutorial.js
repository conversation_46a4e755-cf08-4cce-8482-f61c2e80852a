(async () => {
    // --- STYLES ---
    // Injected into the document head to style the tutorial components.
    const TUTORIAL_OVERLAY_CLASS = 'tutorial-overlay';
    const TUTORIAL_HIGHLIGHT_CLASS = 'tutorial-highlight';
    const TUTORIAL_ANNOTATION_CLASS = 'tutorial-annotation';
    const TUTORIAL_ANNOTATION_SHOW_CLASS = 'show';
    const TUTORIAL_TYPING_CURSOR_CLASS = 'tutorial-typing-cursor';
    const TYPING_CURSOR_BLINK_ANIMATION = 'blink-caret';

    const tutorialStyles = `
        .${TUTORIAL_OVERLAY_CLASS} {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(15, 23, 42, 0.85);
            z-index: 9999;
            pointer-events: none;
        }
        .${TUTORIAL_HIGHLIGHT_CLASS} {
            position: absolute;
            border: 3px solid #8a2be2; /* BlueViolet */
            border-radius: 8px;
            z-index: 10000;
            transition: all 0.5s ease-in-out;
            pointer-events: none;
            box-shadow: 0 0 15px rgba(138, 43, 226, 0.8);
        }
        .${TUTORIAL_ANNOTATION_CLASS} {
            position: absolute;
            background: #333;
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Inter', sans-serif;
            font-size: 16px;
            max-width: 350px;
            text-align: left;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            z-index: 10001;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.3s ease-out, transform 0.3s ease-out;
            pointer-events: none;
            visibility: hidden;
        }
        .${TUTORIAL_ANNOTATION_CLASS}.${TUTORIAL_ANNOTATION_SHOW_CLASS} {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }
        .${TUTORIAL_ANNOTATION_CLASS}::before {
            content: '';
            position: absolute;
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-bottom: 10px solid #333;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
        }
        .${TUTORIAL_TYPING_CURSOR_CLASS} {
            display: inline-block;
            width: 2px;
            height: 1em;
            background-color: white;
            animation: ${TYPING_CURSOR_BLINK_ANIMATION} 0.75s step-end infinite;
            vertical-align: middle;
            margin-left: 2px;
        }
        @keyframes ${TYPING_CURSOR_BLINK_ANIMATION} {
            from, to { background-color: transparent }
            50% { background-color: white; }
        }
    `;

    // --- CONSTANTS ---
    const TUTORIAL_OVERLAY_ID = 'tutorial-overlay';
    const TUTORIAL_HIGHLIGHT_ID = 'tutorial-highlight';
    const TUTORIAL_ANNOTATION_ID = 'tutorial-annotation';
    const TYPING_TEXT_ID = 'typing-text';
    const TUTORIAL_STYLES_ID = 'tutorial-styles';
    const TYPING_SPEED_MS = 25;
    const ANNOTATION_OFFSET_PX = 20;
    const HIGHLIGHT_OFFSET = { x: -10, y: -10, width: 20, height: 20 };
    const WELCOME_MODAL_SELECTOR = '#welcome-modal';
    const WELCOME_MODAL_BODY_SELECTOR = '#welcome-modal-body';
    const START_TOUR_BUTTON_SELECTOR = '#start-tour-button';
    const CLOSE_WELCOME_MODAL_BUTTON_SELECTOR = '#close-welcome-modal-button';

    // --- CORE FUNCTIONS ---
    // These functions manage the lifecycle and display of the tutorial.

    const addStyles = () => {
        if (document.getElementById(TUTORIAL_STYLES_ID)) return;
        const styleElement = document.createElement('style');
        styleElement.id = TUTORIAL_STYLES_ID;
        styleElement.innerHTML = tutorialStyles;
        document.head.appendChild(styleElement);
    };

    const removeStyles = () => {
        const styleElement = document.getElementById(TUTORIAL_STYLES_ID);
        if (styleElement) styleElement.remove();
    };

    let tutorialOverlay = null;
    let tutorialHighlight = null;
    let tutorialAnnotation = null;

    const createOverlay = () => {
        if (tutorialOverlay) return;

        tutorialOverlay = document.createElement('div');
        tutorialOverlay.className = TUTORIAL_OVERLAY_CLASS;
        tutorialOverlay.id = TUTORIAL_OVERLAY_ID;
        document.body.appendChild(tutorialOverlay);

        tutorialHighlight = document.createElement('div');
        tutorialHighlight.className = TUTORIAL_HIGHLIGHT_CLASS;
        tutorialHighlight.id = TUTORIAL_HIGHLIGHT_ID;
        document.body.appendChild(tutorialHighlight);

        tutorialAnnotation = document.createElement('div');
        tutorialAnnotation.className = TUTORIAL_ANNOTATION_CLASS;
        tutorialAnnotation.id = TUTORIAL_ANNOTATION_ID;
        document.body.appendChild(tutorialAnnotation);
    };

    const removeOverlay = () => {
        if (tutorialOverlay) {
            tutorialOverlay.remove();
            tutorialOverlay = null;
        }
        if (tutorialHighlight) {
            tutorialHighlight.remove();
            tutorialHighlight = null;
        }
        if (tutorialAnnotation) {
            tutorialAnnotation.remove();
            tutorialAnnotation = null;
        }
    };

    const highlightElement = (element, offset = { x: 0, y: 0, width: 0, height: 0 }) => {
        if (!tutorialHighlight || !element) return;
        const rect = element.getBoundingClientRect();
        tutorialHighlight.style.left = `${rect.left + window.scrollX + offset.x}px`;
        tutorialHighlight.style.top = `${rect.top + window.scrollY + offset.y}px`;
        tutorialHighlight.style.width = `${rect.width + offset.width}px`;
        tutorialHighlight.style.height = `${rect.height + offset.height}px`;
        tutorialHighlight.style.opacity = '1';
    };

    const typeText = async (text, element) => {
        if (!element) return;
        element.textContent = '';
        for (let i = 0; i < text.length; i++) {
            element.textContent += text.charAt(i);
            await new Promise(resolve => setTimeout(resolve, TYPING_SPEED_MS));
        }
    };

    const showAnnotation = async (text, element, position = 'top') => {
        if (!tutorialAnnotation || !element) return;

        // Set text content to measure dimensions correctly
        tutorialAnnotation.innerHTML = `<span id="${TYPING_TEXT_ID}">${text}</span><span class="tutorial-typing-cursor"></span>`;
        
        // Calculate position with final dimensions
        const rect = element.getBoundingClientRect();
        // Recalculate annotationRect after content is set
        const annotationRect = tutorialAnnotation.getBoundingClientRect();
        let top, left;

        switch (position) {
            case 'bottom':
                top = rect.bottom + window.scrollY + ANNOTATION_OFFSET_PX;
                left = rect.left + window.scrollX + rect.width / 2 - annotationRect.width / 2;
                break;
            case 'left':
                top = rect.top + window.scrollY + rect.height / 2 - annotationRect.height / 2;
                left = rect.left + window.scrollX - annotationRect.width - ANNOTATION_OFFSET_PX;
                break;
            case 'right':
                top = rect.top + window.scrollY + rect.height / 2 - annotationRect.height / 2;
                left = rect.right + window.scrollX + ANNOTATION_OFFSET_PX;
                break;
            case 'top':
            default:
                top = rect.top + window.scrollY - annotationRect.height - ANNOTATION_OFFSET_PX;
                left = rect.left + window.scrollX + rect.width / 2 - annotationRect.width / 2;
                break;
        }

        tutorialAnnotation.style.top = `${top}px`;
        tutorialAnnotation.style.left = `${left}px`;
        tutorialAnnotation.classList.add(TUTORIAL_ANNOTATION_SHOW_CLASS);

        // Animate typing effect
        const typingTextElement = document.getElementById(TYPING_TEXT_ID);
        await typeText(text, typingTextElement);
    };
    
    const hideAnnotation = () => {
        if (tutorialAnnotation) tutorialAnnotation.classList.remove(TUTORIAL_ANNOTATION_SHOW_CLASS);
    };

    const waitForElement = (selector, timeout = 5000) => {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            const check = () => {
                let element;
                if (selector.startsWith('#')) {
                    element = document.getElementById(selector.substring(1));
                } else {
                    element = document.querySelector(selector);
                }

                if (element && (element.offsetParent !== null || element.getClientRects().length > 0)) {
                    resolve(element);
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error(`Element with selector "${selector}" not visible within ${timeout}ms.`));
                } else {
                    requestAnimationFrame(check);
                }
            };
            check();
        });
    };

    // --- TUTORIAL STEPS ---
    // Each object defines a step. Uses `data-tutorial-id` for robust targeting.
    // NOTE: Your application HTML needs to have these `data-tutorial-id` attributes.
    const tutorialSteps = [
        {
            description: "Welcome! This tutorial will guide you through the Welcome Modal.",
            action: async () => {
                await showAnnotation("Welcome to PulseCraft! Press the right arrow key to continue.", document.body, 'center');
            }
        },
        {
            description: "This is the Welcome Modal, your first step into PulseCraft.",
            action: async () => {
                const modal = await waitForElement(WELCOME_MODAL_SELECTOR);
                highlightElement(modal, HIGHLIGHT_OFFSET);
                await showAnnotation("This is the Welcome Modal. It appears the first time you log in.", modal, 'bottom');
            }
        },
        {
            description: "The modal explains the next steps for setting up your brand strategy.",
            action: async () => {
                const modalBody = await waitForElement(WELCOME_MODAL_BODY_SELECTOR);
                highlightElement(modalBody);
                await showAnnotation("Here, we'll briefly explain the onboarding process.", modalBody, 'bottom');
            }
        },
        {
            description: "You can start a guided tour to learn how to make the most of PulseCraft.",
            action: async () => {
                const startButton = await waitForElement(START_TOUR_BUTTON_SELECTOR);
                highlightElement(startButton);
                await showAnnotation("Click here to begin a detailed, step-by-step tour of the application.", startButton, 'bottom');
            }
        },
        {
            description: "Or, you can close the modal and explore on your own.",
            action: async () => {
                const closeButton = await waitForElement(CLOSE_WELCOME_MODAL_BUTTON_SELECTOR);
                highlightElement(closeButton);
                await showAnnotation("If you prefer to explore on your own, you can close this modal.", closeButton, 'right');
            }
        },
        {
            description: "This concludes the initial welcome tour.",
            action: async () => {
                hideAnnotation();
                if (tutorialHighlight) tutorialHighlight.style.opacity = '0';
                await showAnnotation("Enjoy your PulseCraft journey! Press 'esc' to exit.", document.body, 'center');
            }
        }
    ];

    // --- TUTORIAL CONTROLLER ---
    // Manages the state and execution of the tutorial.

    let currentStep = 0;
    let isRunning = false;

    const handleKeyDown = (event) => {
        if (!isRunning) return;
        event.preventDefault();
        event.stopPropagation();

        if (event.key === 'ArrowRight') {
            if (currentStep < tutorialSteps.length - 1) {
                currentStep++;
                runStep(currentStep);
            } else {
                endTutorial("Tutorial finished!");
            }
        } else if (event.key === 'ArrowLeft') {
            if (currentStep > 0) {
                currentStep--;
                runStep(currentStep);
            }
        } else if (event.key === 'Escape') {
            endTutorial("Tutorial exited by user.");
        }
    };

    const runStep = async (stepIndex) => {
        if (stepIndex < 0 || stepIndex >= tutorialSteps.length) return;
        const step = tutorialSteps[stepIndex];
        console.log(`Running step ${stepIndex + 1}/${tutorialSteps.length}: ${step.description}`);

        hideAnnotation();
        if (tutorialHighlight) tutorialHighlight.style.opacity = '0';
        
        try {
            await step.action();
        } catch (error) {
            console.error(`Error in tutorial step ${stepIndex + 1}: ${step.description}`, error);
            endTutorial("A required element was not found. Ending tutorial.");
        }
    };

    const endTutorial = (message = "Tutorial finished.") => {
        if (!isRunning) return;
        isRunning = false;
        removeOverlay();
        removeStyles();
        document.removeEventListener('keydown', handleKeyDown, true);
        console.log(message);
    };

    const startTutorial = () => {
        if (isRunning) {
            console.log("Tutorial is already running.");
            return;
        }
        isRunning = true;
        addStyles();
        createOverlay();
        // Use capturing to ensure we get the key events first
        document.addEventListener('keydown', handleKeyDown, true);
        currentStep = 0;
        runStep(currentStep);
    };

    // Expose the start function to the global scope to be called from anywhere
    window.startFirstLoginTutorial = startTutorial;

    // Example of how to start it automatically.
    // You can remove this and call `window.startFirstLoginTutorial()` when ready.
    startTutorial();
})();
