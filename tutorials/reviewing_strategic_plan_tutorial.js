(async () => {
    const tutorialStyles = `
        .tutorial-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(15, 23, 42, 0.85); /* Darker, but less opaque than pure black */
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
            pointer-events: none; /* Allow clicks to pass through by default */
        }
        .tutorial-highlight {
            position: absolute;
            border: 3px solid #8a2be2; /* BlueViolet */
            border-radius: 8px;
            z-index: 10000;
            transition: all 0.5s ease-in-out;
            pointer-events: none;
            box-shadow: 0 0 15px rgba(138, 43, 226, 0.8);
        }
        .tutorial-annotation {
            position: absolute;
            background: #333;
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Inter', sans-serif;
            font-size: 16px;
            max-width: 350px;
            text-align: left;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            z-index: 10001;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.3s ease-out, transform 0.3s ease-out;
            pointer-events: none;
        }
        .tutorial-annotation.show {
            opacity: 1;
            transform: translateY(0);
        }
        .tutorial-annotation::before {
            content: '';
            position: absolute;
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-bottom: 10px solid #333;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
        }
        .tutorial-button-overlay {
            position: absolute;
            background: rgba(138, 43, 226, 0.3); /* BlueViolet with transparency */
            border-radius: 8px;
            z-index: 10002;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        .tutorial-button-overlay:hover {
            background: rgba(138, 43, 226, 0.5);
        }
        .tutorial-typing-cursor {
            display: inline-block;
            width: 2px;
            height: 1em;
            background-color: white;
            animation: blink-caret 0.75s step-end infinite;
            vertical-align: middle;
            margin-left: 2px;
        }
        @keyframes blink-caret {
            from, to { background-color: transparent }
            50% { background-color: white; }
        }
    `;

    const addStyles = () => {
        const styleElement = document.createElement('style');
        styleElement.id = 'tutorial-styles';
        styleElement.innerHTML = tutorialStyles;
        document.head.appendChild(styleElement);
    };

    const removeStyles = () => {
        const styleElement = document.getElementById('tutorial-styles');
        if (styleElement) {
            styleElement.remove();
        }
    };

    const createOverlay = () => {
        const overlay = document.createElement('div');
        overlay.className = 'tutorial-overlay';
        overlay.id = 'tutorial-overlay';
        document.body.appendChild(overlay);

        const highlight = document.createElement('div');
        highlight.className = 'tutorial-highlight';
        highlight.id = 'tutorial-highlight';
        document.body.appendChild(highlight);

        const annotation = document.createElement('div');
        annotation.className = 'tutorial-annotation';
        annotation.id = 'tutorial-annotation';
        document.body.appendChild(annotation);
    };

    const removeOverlay = () => {
        const overlay = document.getElementById('tutorial-overlay');
        const highlight = document.getElementById('tutorial-highlight');
        const annotation = document.getElementById('tutorial-annotation');
        if (overlay) overlay.remove();
        if (highlight) highlight.remove();
        if (annotation) annotation.remove();
    };

    const highlightElement = (element, offset = { x: 0, y: 0, width: 0, height: 0 }) => {
        const highlight = document.getElementById('tutorial-highlight');
        if (!highlight || !element) return;

        const rect = element.getBoundingClientRect();
        highlight.style.left = `${rect.left + window.scrollX + offset.x}px`;
        highlight.style.top = `${rect.top + window.scrollY + offset.y}px`;
        highlight.style.width = `${rect.width + offset.width}px`;
        highlight.style.height = `${rect.height + offset.height}px`;
        highlight.style.opacity = '1';
    };

    const showAnnotation = (text, element, position = 'top') => {
        const annotation = document.getElementById('tutorial-annotation');
        if (!annotation || !element) return;

        annotation.innerHTML = `<span id="typing-text"></span><span class="tutorial-typing-cursor"></span>`;
        annotation.classList.add('show');

        const rect = element.getBoundingClientRect();
        let top, left;

        if (position === 'top') {
            top = rect.top + window.scrollY - annotation.offsetHeight - 30;
            left = rect.left + window.scrollX + rect.width / 2 - annotation.offsetWidth / 2;
        } else if (position === 'bottom') {
            top = rect.bottom + window.scrollY + 30;
            left = rect.left + window.scrollX + rect.width / 2 - annotation.offsetWidth / 2;
        } else if (position === 'left') {
            top = rect.top + window.scrollY + rect.height / 2 - annotation.offsetHeight / 2;
            left = rect.left + window.scrollX - annotation.offsetWidth - 30;
        } else if (position === 'right') {
            top = rect.top + window.scrollY + rect.height / 2 - annotation.offsetHeight / 2;
            left = rect.right + window.scrollX + 30;
        }

        annotation.style.top = `${top}px`;
        annotation.style.left = `${left}px`;

        typeText(text);
    };

    const hideAnnotation = () => {
        const annotation = document.getElementById('tutorial-annotation');
        if (annotation) {
            annotation.classList.remove('show');
        }
    };

    const typeText = async (text) => {
        const typingTextElement = document.getElementById('typing-text');
        if (!typingTextElement) return;

        typingTextElement.textContent = '';
        for (let i = 0; i < text.length; i++) {
            typingTextElement.textContent += text.charAt(i);
            await new Promise(resolve => setTimeout(resolve, 30)); // Typing speed
        }
    };

    const waitForElement = (selectors, timeout = 10000) => {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            const checkElement = () => {
                for (const selector of selectors) {
                    const element = document.querySelector(selector);
                    if (element && element.offsetParent !== null) { // Check if element is visible
                        return resolve(element);
                    }
                }
                if (Date.now() - startTime > timeout) {
                    return reject(new Error(`Element not found or visible within timeout for selectors: ${selectors.join(', ')}`));
                }
                requestAnimationFrame(checkElement);
            };
            checkElement();
        });
    };

    const simulateClick = (element) => {
        if (element) {
            element.click();
        }
    };

    const tutorialSteps = [
        {
            description: "Welcome to the Strategic Plan Summary! This tutorial will show you how to review your generated content plan.",
            action: async () => {
                // Ensure the Plan Summary Panel is open
                const planSummaryButton = await waitForElement(['button[aria-label="Open plan summary"]', 'button:contains("Strategic Plan Summary")']); // Assuming there's a button to open it
                if (planSummaryButton) simulateClick(planSummaryButton); // Simulate click to open if not already open
                await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for modal to open
                const planSummaryPanel = await waitForElement(['aside.fixed.top-0.right-0.h-full.w-full.max-w-lg.bg-white.shadow-2xl']);
                highlightElement(planSummaryPanel, { x: -10, y: -10, width: 20, height: 20 });
                showAnnotation("This panel summarizes your comprehensive strategic content plan.", planSummaryPanel, 'left');
                await new Promise(resolve => setTimeout(resolve, 4000));
            },
            voiceover: "Welcome to the Strategic Plan Summary! This tutorial will show you how to review your generated content plan."
        },
        {
            description: "The summary is organized by year, quarter, and month, allowing for easy navigation.",
            action: async () => {
                const yearAccordion = await waitForElement(['h3.text-lg.font-bold.text-slate-800:contains("2025")']);
                highlightElement(yearAccordion.parentElement, { x: -10, y: -10, width: 20, height: 20 });
                showAnnotation("Your plan is structured by year, quarter, and month.", yearAccordion.parentElement, 'bottom');
                await new Promise(resolve => setTimeout(resolve, 3000));
            },
            voiceover: "The summary is organized by year, quarter, and month, allowing for easy navigation."
        },
        {
            description: "Expand each section to view the detailed strategy for each quarter and individual week.",
            action: async () => {
                const quarterAccordion = await waitForElement(['h3.text-md.font-semibold.text-slate-700:contains("Q1")']);
                highlightElement(quarterAccordion.parentElement, { x: -10, y: -10, width: 20, height: 20 });
                showAnnotation("Expand to see detailed strategies for each period.", quarterAccordion.parentElement, 'bottom');
                simulateClick(quarterAccordion); // Open quarter
                await new Promise(resolve => setTimeout(resolve, 2000));
                const weekSummary = await waitForElement(['.bg-slate-50.p-4.rounded-lg.border.border-slate-200']);
                highlightElement(weekSummary, { x: -10, y: -10, width: 20, height: 20 });
                showAnnotation("Each week outlines its specific content strategy.", weekSummary, 'bottom');
                await new Promise(resolve => setTimeout(resolve, 3000));
            },
            voiceover: "Expand each section to view the detailed strategy for each quarter and individual week."
        },
        {
            description: "The 'Close' button at the top right allows you to dismiss the summary panel.",
            action: async () => {
                const closeButton = await waitForElement(['button[aria-label="Close plan summary"]', 'button.p-1.rounded-full.text-slate-500']);
                highlightElement(closeButton);
                showAnnotation("Click here to close the plan summary.", closeButton, 'left');
                // Do not actually click to avoid closing the tutorial prematurely
                await new Promise(resolve => setTimeout(resolve, 3000));
            },
            voiceover: "The 'Close' button at the top right allows you to dismiss the summary panel."
        },
        {
            description: "This concludes the tutorial on reviewing your strategic plan. Use this summary to keep your content aligned with your brand goals!",
            action: async () => {
                hideAnnotation();
                const highlight = document.getElementById('tutorial-highlight');
                if (highlight) highlight.style.opacity = '0';
                await new Promise(resolve => setTimeout(resolve, 2000));
            },
            voiceover: "This concludes the tutorial on reviewing your strategic plan. Use this summary to keep your content aligned with your brand goals!"
        }
    ];

    let currentStep = 0;
    const handleKeyDown = (event) => {
        if (event.key === 'ArrowRight') {
            event.preventDefault(); // Prevent default scrolling behavior
            if (currentStep < tutorialSteps.length - 1) {
                // Advance to next step
                currentStep++;
                runStep(currentStep);
            } else {
                // End tutorial if it's the last step
                endTutorial();
            }
        } else if (event.key === 'ArrowLeft') {
            event.preventDefault(); // Prevent default scrolling behavior
            if (currentStep > 0) {
                // Go back to previous step
                currentStep--;
                runStep(currentStep);
            }
        }
    };

    const runStep = async (stepIndex) => {
        if (stepIndex < 0 || stepIndex >= tutorialSteps.length) {
            return; // Out of bounds
        }

        const step = tutorialSteps[stepIndex];
        console.log(`Running step ${stepIndex + 1}/${tutorialSteps.length}: ${step.description}`);

        // Clear previous annotation and highlight
        hideAnnotation();
        const highlight = document.getElementById('tutorial-highlight');
        if (highlight) highlight.style.opacity = '0';

        await step.action();
        await new Promise(resolve => setTimeout(resolve, 1000)); // Pause between steps
    };

    const endTutorial = () => {
        removeOverlay();
        removeStyles();
        document.removeEventListener('keydown', handleKeyDown);
        console.log("Tutorial finished!");
    };

    const runTutorial = async () => {
        addStyles();
        createOverlay();
        document.addEventListener('keydown', handleKeyDown);

        // Run the first step
        await runStep(currentStep);
    };

    // Start the tutorial
    runTutorial();
})();
