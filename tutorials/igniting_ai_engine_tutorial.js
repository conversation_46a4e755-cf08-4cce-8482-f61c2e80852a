(async () => {
    const tutorialStyles = `
        .tutorial-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
            pointer-events: none; /* Allow clicks to pass through by default */
        }
        .tutorial-highlight {
            position: absolute;
            border: 3px solid #8a2be2; /* BlueViolet */
            border-radius: 8px;
            z-index: 10000;
            transition: all 0.5s ease-in-out;
            pointer-events: none;
            box-shadow: 0 0 15px rgba(138, 43, 226, 0.8);
        }
        .tutorial-annotation {
            position: absolute;
            background: #333;
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Inter', sans-serif;
            font-size: 16px;
            max-width: 350px;
            text-align: left;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            z-index: 10001;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.3s ease-out, transform 0.3s ease-out;
            pointer-events: none;
        }
        .tutorial-annotation.show {
            opacity: 1;
            transform: translateY(0);
        }
        .tutorial-annotation::before {
            content: '';
            position: absolute;
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-bottom: 10px solid #333;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
        }
        .tutorial-button-overlay {
            position: absolute;
            background: rgba(138, 43, 226, 0.3); /* BlueViolet with transparency */
            border-radius: 8px;
            z-index: 10002;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        .tutorial-button-overlay:hover {
            background: rgba(138, 43, 226, 0.5);
        }
        .tutorial-typing-cursor {
            display: inline-block;
            width: 2px;
            height: 1em;
            background-color: white;
            animation: blink-caret 0.75s step-end infinite;
            vertical-align: middle;
            margin-left: 2px;
        }
        @keyframes blink-caret {
            from, to { background-color: transparent }
            50% { background-color: white; }
        }
    `;

    const addStyles = () => {
        const styleElement = document.createElement('style');
        styleElement.id = 'tutorial-styles';
        styleElement.innerHTML = tutorialStyles;
        document.head.appendChild(styleElement);
    };

    const removeStyles = () => {
        const styleElement = document.getElementById('tutorial-styles');
        if (styleElement) {
            styleElement.remove();
        }
    };

    const createOverlay = () => {
        const overlay = document.createElement('div');
        overlay.className = 'tutorial-overlay';
        overlay.id = 'tutorial-overlay';
        document.body.appendChild(overlay);

        const highlight = document.createElement('div');
        highlight.className = 'tutorial-highlight';
        highlight.id = 'tutorial-highlight';
        document.body.appendChild(highlight);

        const annotation = document.createElement('div');
        annotation.className = 'tutorial-annotation';
        annotation.id = 'tutorial-annotation';
        document.body.appendChild(annotation);
    };

    const removeOverlay = () => {
        const overlay = document.getElementById('tutorial-overlay');
        const highlight = document.getElementById('tutorial-highlight');
        const annotation = document.getElementById('tutorial-annotation');
        if (overlay) overlay.remove();
        if (highlight) highlight.remove();
        if (annotation) annotation.remove();
    };

    const highlightElement = (element, offset = { x: 0, y: 0, width: 0, height: 0 }) => {
        const highlight = document.getElementById('tutorial-highlight');
        if (!highlight || !element) return;

        const rect = element.getBoundingClientRect();
        highlight.style.left = `${rect.left + window.scrollX + offset.x}px`;
        highlight.style.top = `${rect.top + window.scrollY + offset.y}px`;
        highlight.style.width = `${rect.width + offset.width}px`;
        highlight.style.height = `${rect.height + offset.height}px`;
        highlight.style.opacity = '1';
    };

    const showAnnotation = (text, element, position = 'top') => {
        const annotation = document.getElementById('tutorial-annotation');
        if (!annotation || !element) return;

        annotation.innerHTML = `<span id="typing-text"></span><span class="tutorial-typing-cursor"></span>`;
        annotation.classList.add('show');

        const rect = element.getBoundingClientRect();
        let top, left;

        if (position === 'top') {
            top = rect.top + window.scrollY - annotation.offsetHeight - 30;
            left = rect.left + window.scrollX + rect.width / 2 - annotation.offsetWidth / 2;
        } else if (position === 'bottom') {
            top = rect.bottom + window.scrollY + 30;
            left = rect.left + window.scrollX + rect.width / 2 - annotation.offsetWidth / 2;
        } else if (position === 'left') {
            top = rect.top + window.scrollY + rect.height / 2 - annotation.offsetHeight / 2;
            left = rect.left + window.scrollX - annotation.offsetWidth - 30;
        } else if (position === 'right') {
            top = rect.top + window.scrollY + rect.height / 2 - annotation.offsetHeight / 2;
            left = rect.right + window.scrollX + 30;
        }

        annotation.style.top = `${top}px`;
        annotation.style.left = `${left}px`;

        typeText(text);
    };

    const hideAnnotation = () => {
        const annotation = document.getElementById('tutorial-annotation');
        if (annotation) {
            annotation.classList.remove('show');
        }
    };

    const typeText = async (text) => {
        const typingTextElement = document.getElementById('typing-text');
        if (!typingTextElement) return;

        typingTextElement.textContent = '';
        for (let i = 0; i < text.length; i++) {
            typingTextElement.textContent += text.charAt(i);
            await new Promise(resolve => setTimeout(resolve, 30)); // Typing speed
        }
    };

    const waitForElement = (selectors, timeout = 10000) => {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            const checkElement = () => {
                for (const selector of selectors) {
                    const element = document.querySelector(selector);
                    if (element && element.offsetParent !== null) { // Check if element is visible
                        return resolve(element);
                    }
                }
                if (Date.now() - startTime > timeout) {
                    return reject(new Error(`Element not found or visible within timeout for selectors: ${selectors.join(', ')}`));
                }
                requestAnimationFrame(checkElement);
            };
            checkElement();
        });
    };

    const simulateClick = (element) => {
        if (element) {
            element.click();
        }
    };

    const tutorialSteps = [
        {
            description: "Welcome to the Content Planner! This tutorial will show you how to manage and create your posts.",
            action: async () => {
                // Ensure we are on the content planner view
                const plannerNav = await waitForElement(['button[data-tour-id="nav-planner"]', 'button:contains("Content Planner")']);
                simulateClick(plannerNav);
                await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for view transition
            },
            voiceover: "Welcome to the Content Planner! This tutorial will show you how to manage and create your posts."
        },
        {
            description: "The 'New Post' button allows you to create a new post for any connected social media platform.",
            action: async () => {
                const newPostButton = await waitForElement(['button:contains("New Post")', 'button[variant="primary"][size="sm"]:has(svg.w-4.h-4.mr-2)']);
                highlightElement(newPostButton);
                showAnnotation("Click here to create a new post.", newPostButton, 'bottom');
                simulateClick(newPostButton); // Open the dropdown
                await new Promise(resolve => setTimeout(resolve, 3000));
            },
            voiceover: "The 'New Post' button allows you to create a new post for any connected social media platform."
        },
        {
            description: "Select a platform from the dropdown, like LinkedIn, to start drafting your content.",
            action: async () => {
                const linkedinOption = await waitForElement(['button.w-full.text-left.px-3.py-2.text-sm.hover\\:bg-slate-50:contains("LinkedIn")', 'button:contains("LinkedIn")']);
                highlightElement(linkedinOption);
                showAnnotation("Choose your desired platform to create a new post.", linkedinOption, 'right');
                // Do not actually click to avoid opening the post modal
                await new Promise(resolve => setTimeout(resolve, 3000));
            },
            voiceover: "Select a platform from the dropdown, like LinkedIn, to start drafting your content."
        },
        {
            description: "The 'Download All Posts' button allows you to export all your content, including images, as a zip file.",
            action: async () => {
                const downloadButton = await waitForElement(['button:contains("Download All Posts")', 'button[variant="outline"][size="sm"]:has(svg.w-4.h-4.mr-2)']);
                highlightElement(downloadButton);
                showAnnotation("Export all your posts and images as a zip file.", downloadButton, 'bottom');
                await new Promise(resolve => setTimeout(resolve, 3000));
            },
            voiceover: "The 'Download All Posts' button allows you to export all your content, including images, as a zip file."
        },
        {
            description: "You can switch between different views of your content: Grid, Agenda, Month, Week, and Day.",
            action: async () => {
                const viewToggles = await waitForElement(['.inline-flex.items-center.bg-slate-200.p-1.rounded-lg']);
                highlightElement(viewToggles, { x: -10, y: -10, width: 20, height: 20 });
                showAnnotation("Choose how you want to view your content schedule.", viewToggles, 'bottom');
                await new Promise(resolve => setTimeout(resolve, 3000));
            },
            voiceover: "You can switch between different views of your content: Grid, Agenda, Month, Week, and Day."
        },
        {
            description: "The 'Grid' view provides a visual overview of your posts.",
            action: async () => {
                const gridButton = await waitForElement(['button:contains("Grid")', 'button[title="Grid View"]']);
                highlightElement(gridButton);
                showAnnotation("The Grid view shows your posts in a visual layout.", gridButton, 'bottom');
                simulateClick(gridButton);
                await new Promise(resolve => setTimeout(resolve, 2000));
            },
            voiceover: "The 'Grid' view provides a visual overview of your posts."
        },
        {
            description: "The 'Agenda' view lists your scheduled posts in a chronological order.",
            action: async () => {
                const agendaButton = await waitForElement(['button:contains("Agenda")', 'button[title="Agenda View"]']);
                highlightElement(agendaButton);
                showAnnotation("The Agenda view lists your posts chronologically.", agendaButton, 'bottom');
                simulateClick(agendaButton);
                await new Promise(resolve => setTimeout(resolve, 2000));
            },
            voiceover: "The 'Agenda' view lists your scheduled posts in a chronological order."
        },
        {
            description: "The 'Month', 'Week', and 'Day' views offer calendar-based perspectives of your content schedule.",
            action: async () => {
                const monthButton = await waitForElement(['button:contains("Month")', 'button[title="Month View"]']);
                const weekButton = await waitForElement(['button:contains("Week")', 'button[title="Week View"]']);
                const dayButton = await waitForElement(['button:contains("Day")', 'button[title="Day View"]']);

                highlightElement(monthButton);
                showAnnotation("Calendar views for Month, Week, and Day.", monthButton, 'bottom');
                simulateClick(monthButton);
                await new Promise(resolve => setTimeout(resolve, 1000));

                highlightElement(weekButton);
                simulateClick(weekButton);
                await new Promise(resolve => setTimeout(resolve, 1000));

                highlightElement(dayButton);
                simulateClick(dayButton);
                await new Promise(resolve => setTimeout(resolve, 2000));
            },
            voiceover: "The 'Month', 'Week', and 'Day' views offer calendar-based perspectives of your content schedule."
        },
        {
            description: "This concludes the Content Planner tutorial. Start organizing your content with ease!",
            action: async () => {
                hideAnnotation();
                const highlight = document.getElementById('tutorial-highlight');
                if (highlight) highlight.style.opacity = '0';
                await new Promise(resolve => setTimeout(resolve, 2000));
            },
            voiceover: "This concludes the Content Planner tutorial. Start organizing your content with ease!"
        }
    ];

    const runTutorial = async () => {
        addStyles();
        createOverlay();

        for (const step of tutorialSteps) {
            console.log(`Running step: ${step.description}`);
            await step.action();
            await new Promise(resolve => setTimeout(resolve, 1000)); // Pause between steps
        }

        removeOverlay();
        removeStyles();
        console.log("Tutorial finished!");
    };

    // Start the tutorial
    runTutorial();
})();
