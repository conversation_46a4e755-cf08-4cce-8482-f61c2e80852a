(async () => {
    const tutorialStyles = `
        .tutorial-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
            pointer-events: none; /* Allow clicks to pass through by default */
        }
        .tutorial-highlight {
            position: absolute;
            border: 3px solid #8a2be2; /* BlueViolet */
            border-radius: 8px;
            z-index: 10000;
            transition: all 0.5s ease-in-out;
            pointer-events: none;
            box-shadow: 0 0 15px rgba(138, 43, 226, 0.8);
        }
        .tutorial-annotation {
            position: absolute;
            background: #333;
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Inter', sans-serif;
            font-size: 16px;
            max-width: 350px;
            text-align: left;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            z-index: 10001;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.3s ease-out, transform 0.3s ease-out;
            pointer-events: none;
        }
        .tutorial-annotation.show {
            opacity: 1;
            transform: translateY(0);
        }
        .tutorial-annotation::before {
            content: '';
            position: absolute;
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-bottom: 10px solid #333;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
        }
        .tutorial-button-overlay {
            position: absolute;
            background: rgba(138, 43, 226, 0.3); /* BlueViolet with transparency */
            border-radius: 8px;
            z-index: 10002;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        .tutorial-button-overlay:hover {
            background: rgba(138, 43, 226, 0.5);
        }
        .tutorial-typing-cursor {
            display: inline-block;
            width: 2px;
            height: 1em;
            background-color: white;
            animation: blink-caret 0.75s step-end infinite;
            vertical-align: middle;
            margin-left: 2px;
        }
        @keyframes blink-caret {
            from, to { background-color: transparent }
            50% { background-color: white; }
        }
    `;

    const addStyles = () => {
        const styleElement = document.createElement('style');
        styleElement.id = 'tutorial-styles';
        styleElement.innerHTML = tutorialStyles;
        document.head.appendChild(styleElement);
    };

    const removeStyles = () => {
        const styleElement = document.getElementById('tutorial-styles');
        if (styleElement) {
            styleElement.remove();
        }
    };

    const createOverlay = () => {
        const overlay = document.createElement('div');
        overlay.className = 'tutorial-overlay';
        overlay.id = 'tutorial-overlay';
        document.body.appendChild(overlay);

        const highlight = document.createElement('div');
        highlight.className = 'tutorial-highlight';
        highlight.id = 'tutorial-highlight';
        document.body.appendChild(highlight);

        const annotation = document.createElement('div');
        annotation.className = 'tutorial-annotation';
        annotation.id = 'tutorial-annotation';
        document.body.appendChild(annotation);
    };

    const removeOverlay = () => {
        const overlay = document.getElementById('tutorial-overlay');
        const highlight = document.getElementById('tutorial-highlight');
        const annotation = document.getElementById('tutorial-annotation');
        if (overlay) overlay.remove();
        if (highlight) highlight.remove();
        if (annotation) annotation.remove();
    };

    const highlightElement = (element, offset = { x: 0, y: 0, width: 0, height: 0 }) => {
        const highlight = document.getElementById('tutorial-highlight');
        if (!highlight || !element) return;

        const rect = element.getBoundingClientRect();
        highlight.style.left = `${rect.left + window.scrollX + offset.x}px`;
        highlight.style.top = `${rect.top + window.scrollY + offset.y}px`;
        highlight.style.width = `${rect.width + offset.width}px`;
        highlight.style.height = `${rect.height + offset.height}px`;
        highlight.style.opacity = '1';
    };

    const showAnnotation = (text, element, position = 'top') => {
        const annotation = document.getElementById('tutorial-annotation');
        if (!annotation || !element) return;

        annotation.innerHTML = `<span id="typing-text"></span><span class="tutorial-typing-cursor"></span>`;
        annotation.classList.add('show');

        const rect = element.getBoundingClientRect();
        let top, left;

        if (position === 'top') {
            top = rect.top + window.scrollY - annotation.offsetHeight - 30;
            left = rect.left + window.scrollX + rect.width / 2 - annotation.offsetWidth / 2;
        } else if (position === 'bottom') {
            top = rect.bottom + window.scrollY + 30;
            left = rect.left + window.scrollX + rect.width / 2 - annotation.offsetWidth / 2;
        } else if (position === 'left') {
            top = rect.top + window.scrollY + rect.height / 2 - annotation.offsetHeight / 2;
            left = rect.left + window.scrollX - annotation.offsetWidth - 30;
        } else if (position === 'right') {
            top = rect.top + window.scrollY + rect.height / 2 - annotation.offsetHeight / 2;
            left = rect.right + window.scrollX + 30;
        }

        annotation.style.top = `${top}px`;
        annotation.style.left = `${left}px`;

        typeText(text);
    };

    const hideAnnotation = () => {
        const annotation = document.getElementById('tutorial-annotation');
        if (annotation) {
            annotation.classList.remove('show');
        }
    };

    const typeText = async (text) => {
        const typingTextElement = document.getElementById('typing-text');
        if (!typingTextElement) return;

        typingTextElement.textContent = '';
        for (let i = 0; i < text.length; i++) {
            typingTextElement.textContent += text.charAt(i);
            await new Promise(resolve => setTimeout(resolve, 30)); // Typing speed
        }
    };

    const waitForElement = (selectors, timeout = 10000) => {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            const checkElement = () => {
                for (const selector of selectors) {
                    const element = document.querySelector(selector);
                    if (element && element.offsetParent !== null) { // Check if element is visible
                        return resolve(element);
                    }
                }
                if (Date.now() - startTime > timeout) {
                    return reject(new Error(`Element not found or visible within timeout for selectors: ${selectors.join(', ')}`));
                }
                requestAnimationFrame(checkElement);
            };
            checkElement();
        });
    };

    const simulateClick = (element) => {
        if (element) {
            element.click();
        }
    };

    const tutorialSteps = [
        {
            description: "Welcome to the Strategy Brief! This tutorial will show you how to define your brand's DNA and refine your content plan.",
            action: async () => {
                // Ensure we are on the strategy view
                const strategyNav = await waitForElement(['button[data-tour-id="nav-strategy"]', 'button:contains("Strategy Brief")']);
                simulateClick(strategyNav);
                await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for view transition
            },
            voiceover: "Welcome to the Strategy Brief! This tutorial will show you how to define your brand's DNA and refine your content plan."
        },
        {
            description: "The Strategy Brief is where you define your brand's core identity and content strategy.",
            action: async () => {
                const briefView = await waitForElement(['.h-full.flex.flex-col.relative.-mx-6.-my-6']);
                highlightElement(briefView, { x: -10, y: -10, width: 20, height: 20 });
                showAnnotation("This is your actionable brief, guiding your content strategy.", briefView, 'top');
                await new Promise(resolve => setTimeout(resolve, 4000));
            },
            voiceover: "The Strategy Brief is where you define your brand's core identity and content strategy."
        },
        {
            description: "Each section, like '2025', represents a major period in your content plan.",
            action: async () => {
                const yearAccordion = await waitForElement(['.space-y-4 > .rounded-lg.border:first-child h3:contains("2025")']);
                highlightElement(yearAccordion.parentElement, { x: -10, y: -10, width: 20, height: 20 });
                showAnnotation("Each year or major period has its own strategic section.", yearAccordion.parentElement, 'bottom');
                await new Promise(resolve => setTimeout(resolve, 3000));
            },
            voiceover: "Each section, like '2025', represents a major period in your content plan."
        },
        {
            description: "Within each year, you'll find quarters, and then individual weeks, each with its own strategy.",
            action: async () => {
                const quarterAccordion = await waitForElement(['.space-y-3 > .rounded-lg.border:first-child h3:contains("Q1")']);
                highlightElement(quarterAccordion.parentElement, { x: -10, y: -10, width: 20, height: 20 });
                showAnnotation("Content plans are broken down by quarters and weeks.", quarterAccordion.parentElement, 'bottom');
                await new Promise(resolve => setTimeout(resolve, 4000));
            },
            voiceover: "Within each year, you'll find quarters, and then individual weeks, each with its own strategy."
        },
        {
            description: "You can expand each week to see its specific strategy, story idea, and core concepts.",
            action: async () => {
                const weekComponent = await waitForElement(['.grid.grid-cols-1.lg\\:grid-cols-2.gap-3 > .p-4.rounded-lg.border:first-child']);
                highlightElement(weekComponent, { x: -10, y: -10, width: 20, height: 20 });
                showAnnotation("Expand a week to view its detailed content strategy.", weekComponent, 'bottom');
                simulateClick(weekComponent.querySelector('details summary')); // Open details
                await new Promise(resolve => setTimeout(resolve, 3000));
            },
            voiceover: "You can expand each week to see its specific strategy, story idea, and core concepts."
        },
        {
            description: "Use the 'Refine' button to activate the AI Co-pilot and adjust any section of your plan.",
            action: async () => {
                const refineButton = await waitForElement(['button:contains("Refine")', 'button[variant="ghost"][size="xs"]']);
                highlightElement(refineButton);
                showAnnotation("Click 'Refine' to use AI to adjust your plan.", refineButton, 'right');
                simulateClick(refineButton); // Simulate click to open refinement input
                await new Promise(resolve => setTimeout(resolve, 3000));
            },
            voiceover: "Use the 'Refine' button to activate the AI Co-pilot and adjust any section of your plan."
        },
        {
            description: "Type your refinement instructions here, for example, 'Make this more aggressive'.",
            action: async () => {
                const refinementInput = await waitForElement(['textarea.input.w-full.min-h-\\[40px\\]', 'textarea[placeholder="e.g., Make this more aggressive..."]']);
                highlightElement(refinementInput);
                showAnnotation("Enter your refinement instructions here.", refinementInput, 'bottom');
                await new Promise(resolve => setTimeout(resolve, 3000));
            },
            voiceover: "Type your refinement instructions here, for example, 'Make this more aggressive'."
        },
        {
            description: "Click 'Refine' to apply the changes, or close the input if you change your mind.",
            action: async () => {
                const applyRefineButton = await waitForElement(['button:contains("Refine (")']);
                highlightElement(applyRefineButton);
                showAnnotation("Click to apply your refinement.", applyRefineButton, 'right');
                // Do not actually click
                await new Promise(resolve => setTimeout(resolve, 3000));
            },
            voiceover: "Click 'Refine' to apply the changes, or close the input if you change your mind."
        },
        {
            description: "This concludes the tutorial on defining your brand DNA and refining your strategy. Start crafting your perfect plan!",
            action: async () => {
                hideAnnotation();
                const highlight = document.getElementById('tutorial-highlight');
                if (highlight) highlight.style.opacity = '0';
                await new Promise(resolve => setTimeout(resolve, 2000));
            },
            voiceover: "This concludes the tutorial on defining your brand DNA and refining your strategy. Start crafting your perfect plan!"
        }
    ];

    const runTutorial = async () => {
        addStyles();
        createOverlay();

        for (const step of tutorialSteps) {
            console.log(`Running step: ${step.description}`);
            await step.action();
            await new Promise(resolve => setTimeout(resolve, 1000)); // Pause between steps
        }

        removeOverlay();
        removeStyles();
        console.log("Tutorial finished!");
    };

    // Start the tutorial
    runTutorial();
})();
