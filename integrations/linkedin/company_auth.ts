/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * Generates the authorization URL for LinkedIn OAuth 2.0 flow for company pages.
 * @param redirectUri - The URI to redirect to after authorization.
 * @param state - A unique string to prevent CSRF attacks.
 * @returns The full authorization URL.
 */
export const getLinkedInCompanyAuthUrl = (redirectUri: string, state: string): string => {
  const clientId = import.meta.env.VITE_LINKEDIN_CLIENT_ID;
  if (!clientId) {
    throw new Error('VITE_LINKEDIN_CLIENT_ID is not set in the environment variables.');
  }

  const params = new URLSearchParams({
    response_type: 'code',
    client_id: clientId,
    redirect_uri: redirectUri,
    state: state,
    scope: 'r_organization_social w_organization_social',
  });

  return `https://www.linkedin.com/oauth/v2/authorization?${params.toString()}`;
};
