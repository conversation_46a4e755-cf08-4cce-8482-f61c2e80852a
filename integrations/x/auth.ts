/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * Generates the authorization URL for X (Twitter) OAuth 2.0 flow.
 * @param redirectUri - The URI to redirect to after authorization.
 * @param state - A unique string to prevent CSRF attacks.
 * @returns The full authorization URL.
 */
export const getXAuthUrl = (redirectUri: string, state: string): string => {
  const clientId = import.meta.env.VITE_X_CLIENT_ID;
  if (!clientId) {
    throw new Error('VITE_X_CLIENT_ID is not set in the environment variables.');
  }

  const params = new URLSearchParams({
    response_type: 'code',
    client_id: clientId,
    redirect_uri: redirectUri,
    scope: 'tweet.read users.read tweet.write offline.access',
    state: state,
    code_challenge: 'challenge', // Using plain for simplicity, but PKCE is recommended for production
    code_challenge_method: 'plain',
  });

  return `https://twitter.com/i/oauth2/authorize?${params.toString()}`;
};
