/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * Generates the authorization URL for Meta (Facebook & Instagram) OAuth 2.0 flow.
 * @param redirectUri - The URI to redirect to after authorization.
 * @param state - A unique string to prevent CSRF attacks.
 * @returns The full authorization URL.
 */
export const getMetaAuthUrl = (redirectUri: string, state: string): string => {
  const clientId = import.meta.env.VITE_META_CLIENT_ID;
  if (!clientId) {
    throw new Error('VITE_META_CLIENT_ID is not set in the environment variables.');
  }

  const params = new URLSearchParams({
    client_id: clientId,
    redirect_uri: redirectUri,
    state: state,
    scope: 'public_profile,instagram_basic,pages_show_list,instagram_content_publish,pages_read_engagement',
    response_type: 'code',
  });

  return `https://www.facebook.com/v19.0/dialog/oauth?${params.toString()}`;
};
