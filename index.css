/* Global styles for PulseCraft */

/* Mobile Typography Improvements */
@media (max-width: 768px) {
  /* Increase base font sizes on mobile */
  .text-xs { font-size: 0.8rem; }
  .text-sm { font-size: 0.9rem; }
  .text-base { font-size: 1rem; }
  .text-lg { font-size: 1.125rem; }
  .text-xl { font-size: 1.25rem; }

  /* Improve line height for readability */
  body { line-height: 1.6; }

  /* Better spacing for mobile */
  .space-y-1 > * + * { margin-top: 0.375rem; }
  .space-y-2 > * + * { margin-top: 0.625rem; }
  .space-y-3 > * + * { margin-top: 0.875rem; }

  /* Ensure minimum touch targets */
  button, .button, [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }

  /* Better input sizing */
  input, textarea, select {
    min-height: 44px;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Improve modal readability */
  .modal-content {
    font-size: 1rem;
    line-height: 1.6;
  }

  /* Better card spacing */
  .card-mobile {
    padding: 1rem;
    margin-bottom: 1rem;
  }
}

/* High contrast mode improvements */
@media (prefers-contrast: high) {
  .text-slate-400 { color: #64748b; }
  .text-slate-500 { color: #475569; }
  .text-slate-600 { color: #334155; }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}