import React, { useState } from 'react';
import { PlannerView, Post, Platform, ALL_PLATFORMS } from '../types';
import PostGrid from './PostGrid';
import PlatformIcon from './ui/PlatformIcon';
import CalendarView from './CalendarView';
import AgendaView from './AgendaView';
import Button from './ui/Button';
import { GridIcon } from './icons/GridIcon';
import { CalendarIcon } from './icons/CalendarIcon';
import { DownloadIcon } from './icons/DownloadIcon';
import { ClipboardListIcon } from './icons/ClipboardListIcon';
import { WandIcon } from './icons/WandIcon';
import { VideoIcon } from './icons/VideoIcon';
import Loader from './ui/Loader';
import { usePosts, useSettings, useUI } from '../contexts/AppContext';
// @ts-ignore - JSZip is loaded from importmap
import JSZip from 'jszip';


const ContentPlannerView: React.FC = () => {
    const { posts, handleUpdatePost, handleAddNewPost } = usePosts();
    const { settings, loadingStates } = useSettings();
    const { setSelectedPost } = useUI();
    const [view, setView] = useState<PlannerView>('grid');
    const [isDownloading, setIsDownloading] = useState(false);
    const [isNewPostMenuOpen, setNewPostMenuOpen] = useState(false);

    const handleNewPost = (platform: Platform) => {
        const newPost: Post = {
            id: 0, // Temporary ID for a new post
            platform,
            caption: '',
            hashtags: '',
            image_prompt: '',
            likes: 0,
            comments: 0,
            date: new Date().toISOString(),
            status: 'draft',
        };
        setSelectedPost(newPost);
    };

    const handleDownloadAll = async () => {
        setIsDownloading(true);
        try {
            const zip = new JSZip();
            const imageFolder = zip.folder("images");
            let summary = `Content Export for ${settings.brandName}\nExported on: ${new Date().toLocaleString()}\n\n`;

            summary += "====================\n\n";

            for (const post of posts) {
                summary += `POST ID: ${post.id}\n`;
                summary += `PLATFORM: ${post.platform}\n`;
                summary += `SCHEDULED: ${post.scheduledDate ? new Date(post.scheduledDate).toLocaleString() : 'Not Scheduled'}\n`;
                summary += `CAPTION: ${post.caption}\n`;
                summary += `HASHTAGS: ${post.hashtags}\n`;
                
                if (post.imgSrc) {
                    const imageName = `${post.platform}-${post.id}.jpeg`;
                    summary += `IMAGE: images/${imageName}\n`;
                    try {
                        const response = await fetch(post.imgSrc);
                        const blob = await response.blob();
                        imageFolder!.file(imageName, blob);
                    } catch (fetchError) {
                        console.error(`Could not fetch image for post ${post.id}:`, fetchError);
                        summary += `  -> Could not download image file.\n`;
                    }
                } else {
                    summary += `IMAGE: Not generated\n`;
                }
                summary += "\n====================\n\n";
            }
            
            zip.file("summary.txt", summary);

            const content = await zip.generateAsync({ type: "blob" });
            const link = document.createElement("a");
            link.href = URL.createObjectURL(content);
            link.download = `${settings.brandName}_content_export.zip`;
            link.click();
            URL.revokeObjectURL(link.href);

        } catch (error) {
            console.error("Failed to create zip file", error);
            alert("An error occurred while preparing the download.");
        } finally {
            setIsDownloading(false);
        }
    };


    return (
        <div>
            {/* Mobile Header */}
            <div className="lg:hidden mb-6 space-y-4">
                {/* Action Buttons Row */}
                <div className="flex flex-col sm:flex-row gap-2">
                    <div className="relative flex-1">
                        <Button
                            onClick={() => setNewPostMenuOpen(o => !o)}
                            variant="primary"
                            size="sm"
                            className="w-full min-h-[44px] justify-center"
                        >
                            <WandIcon className="w-4 h-4 mr-2" /> New Post
                        </Button>
                        {isNewPostMenuOpen && (
                            <div className="absolute top-full mt-2 w-full bg-white rounded-md shadow-lg border border-slate-200 z-10">
                                {ALL_PLATFORMS.map(p => (
                                    <button
                                        key={p}
                                        onClick={() => {handleNewPost(p); setNewPostMenuOpen(false);}}
                                        className="w-full text-left px-3 py-3 text-sm hover:bg-slate-50 flex items-center space-x-2 min-h-[44px]"
                                    >
                                        <PlatformIcon platform={p} className="w-4 h-4" />
                                        <span className="capitalize">{p === 'x-twitter' ? 'X' : p}</span>
                                    </button>
                                ))}
                            </div>
                        )}
                    </div>
                    <Button
                        onClick={handleDownloadAll}
                        variant="outline"
                        size="sm"
                        disabled={isDownloading || posts.length === 0}
                        className="min-h-[44px] flex-1 sm:flex-none"
                    >
                        {isDownloading ? <Loader size="sm" className="mr-2" /> : <DownloadIcon className="w-4 h-4 mr-2" />}
                        <span className="hidden sm:inline">{isDownloading ? 'Zipping...' : 'Download All'}</span>
                        <span className="sm:hidden">{isDownloading ? 'Zipping...' : 'Download'}</span>
                    </Button>
                </div>

                {/* View Toggle Row */}
                <div className="flex justify-center">
                    <div className="inline-flex items-center bg-slate-200 p-1 rounded-lg w-full max-w-sm">
                        <Button
                            size="sm"
                            variant={view === 'grid' ? 'primary' : 'ghost'}
                            onClick={() => setView('grid')}
                            className={`!rounded-md flex-1 min-h-[44px] ${view === 'grid' ? 'shadow' : 'text-slate-600'}`}
                        >
                            <GridIcon className="w-4 h-4 lg:mr-2" title="Grid View"/>
                            <span className="hidden sm:inline ml-2">Grid</span>
                        </Button>
                        <Button
                            size="sm"
                            variant={view === 'agenda' ? 'primary' : 'ghost'}
                            onClick={() => setView('agenda')}
                            className={`!rounded-md flex-1 min-h-[44px] ${view === 'agenda' ? 'shadow' : 'text-slate-600'}`}
                        >
                            <ClipboardListIcon className="w-4 h-4 lg:mr-2" title="Agenda View"/>
                            <span className="hidden sm:inline ml-2">Agenda</span>
                        </Button>
                        <Button
                            size="sm"
                            variant={view === 'month' ? 'primary' : 'ghost'}
                            onClick={() => setView('month')}
                            className={`!rounded-md flex-1 min-h-[44px] ${view === 'month' ? 'shadow' : 'text-slate-600'}`}
                        >
                            <CalendarIcon className="w-4 h-4 lg:mr-2" title="Month View"/>
                            <span className="hidden sm:inline ml-2">Month</span>
                        </Button>
                    </div>
                </div>
            </div>

            {/* Desktop Header */}
            <div className="hidden lg:flex justify-between items-center mb-6">
                <div className="flex space-x-2">
                    <div className="relative">
                        <Button onClick={() => setNewPostMenuOpen(o => !o)} variant="primary" size="sm">
                            <WandIcon className="w-4 h-4 mr-2" /> New Post
                        </Button>
                        {isNewPostMenuOpen && (
                            <div className="absolute top-full mt-2 w-40 bg-white rounded-md shadow-lg border border-slate-200 z-10">
                                {ALL_PLATFORMS.map(p => (
                                    <button key={p} onClick={() => {handleNewPost(p); setNewPostMenuOpen(false);}} className="w-full text-left px-3 py-2 text-sm hover:bg-slate-50 flex items-center space-x-2">
                                        <PlatformIcon platform={p} className="w-4 h-4" />
                                        <span className="capitalize">{p === 'x-twitter' ? 'X' : p}</span>
                                    </button>
                                ))}
                            </div>
                        )}
                    </div>
                    <Button variant="outline" size="sm" disabled>
                        <VideoIcon className="w-4 h-4 mr-2" /> New Story (Coming Soon)
                    </Button>
                    <Button onClick={handleDownloadAll} variant="outline" size="sm" disabled={isDownloading || posts.length === 0}>
                        {isDownloading ? <Loader size="sm" className="mr-2" /> : <DownloadIcon className="w-4 h-4 mr-2" />}
                        {isDownloading ? 'Zipping...' : 'Download All Posts'}
                    </Button>
                </div>

                <div className="inline-flex items-center bg-slate-200 p-1 rounded-lg">
                    <Button
                        size="sm" variant={view === 'grid' ? 'primary' : 'ghost'} onClick={() => setView('grid')}
                        className={`!rounded-md ${view === 'grid' ? 'shadow' : 'text-slate-600'}`}
                    >
                        <GridIcon className="w-4 h-4 mr-2" title="Grid View"/> Grid
                    </Button>
                    <Button
                        size="sm" variant={view === 'agenda' ? 'primary' : 'ghost'} onClick={() => setView('agenda')}
                        className={`!rounded-md ${view === 'agenda' ? 'shadow' : 'text-slate-600'}`}
                    >
                        <ClipboardListIcon className="w-4 h-4 mr-2" title="Agenda View"/> Agenda
                    </Button>
                     <Button
                        size="sm" variant={view === 'month' ? 'primary' : 'ghost'} onClick={() => setView('month')}
                        className={`!rounded-md ${view === 'month' ? 'shadow' : 'text-slate-600'}`}
                    >
                         <CalendarIcon className="w-4 h-4 mr-2" title="Month View"/> Month
                    </Button>
                    <Button 
                        size="sm" variant={view === 'week' ? 'primary' : 'ghost'} onClick={() => setView('week')}
                        className={`!rounded-md ${view === 'week' ? 'shadow' : 'text-slate-600'}`}
                    >
                         <CalendarIcon className="w-4 h-4 mr-2" title="Week View"/> Week
                    </Button>
                    <Button 
                        size="sm" variant={view === 'day' ? 'primary' : 'ghost'} onClick={() => setView('day')}
                        className={`!rounded-md ${view === 'day' ? 'shadow' : 'text-slate-600'}`}
                    >
                         <CalendarIcon className="w-4 h-4 mr-2" title="Day View"/> Day
                    </Button>
                </div>
            </div>
            
            {view === 'grid' ? (
                <PostGrid />
            ) : view === 'agenda' ? (
                <AgendaView 
                    posts={posts.filter(p => p.scheduledDate)}
                    onPostClick={setSelectedPost}
                />
            ) : (
                <CalendarView 
                    posts={posts.filter(p => p.scheduledDate)} 
                    onPostClick={setSelectedPost}
                    onPostUpdate={handleUpdatePost}
                    viewMode={view}
                />
            )}
        </div>
    );
};

export default ContentPlannerView;
