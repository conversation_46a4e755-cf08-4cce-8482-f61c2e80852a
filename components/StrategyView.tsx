

import React, { useState, useEffect } from 'react';
import GeneratePlanView from './PlannerView';
import BriefView from './BriefView';
import PostGenerationFlow from './PostGenerationFlow';
import { usePlan, useSettings, useUI } from '../contexts/AppContext';

type SubView = 'GENERATE_PLAN' | 'GENERATE_POSTS' | 'BRIEF';

const StrategyView: React.FC = () => {
    const { planMarkdown, completedWeeks, handleGeneratePlan, handleGenerateFirstMonth } = usePlan();
    const { loadingStates } = useSettings();
    const { setView } = useUI();
    
    const [subView, setSubView] = useState<SubView>('GENERATE_PLAN');

    useEffect(() => {
        if (planMarkdown && !loadingStates.plan) {
            const hasCompletedAnyWeek = completedWeeks.length > 0;
            if (hasCompletedAnyWeek) {
                setSubView('BRIEF');
            } else {
                setSubView('GENERATE_POSTS');
            }
        } else {
            setSubView('GENERATE_PLAN');
        }
    }, [planMarkdown, completedWeeks.length, loadingStates.plan]);

    const handleGeneratePlanAndSwitch = async () => {
        const success = await handleGeneratePlan();
        return success;
    };
    
    const handleGenerateFirstMonthAndSwitch = async () => {
        const success = await handleGenerateFirstMonth();
        if (success) {
            setView('planner'); 
        }
        return success;
    };

    const renderSubView = () => {
        switch(subView) {
            case 'GENERATE_PLAN':
                return <GeneratePlanView onGeneratePlan={handleGeneratePlanAndSwitch} />;
            case 'GENERATE_POSTS':
                return (
                    <PostGenerationFlow 
                        onGenerate={handleGenerateFirstMonthAndSwitch}
                        onSkip={() => setSubView('BRIEF')}
                    />
                );
            case 'BRIEF':
                return <BriefView />;
            default:
                return null;
        }
    }

    return (
        <div className="bg-slate-50 rounded-lg border border-slate-200 shadow-sm p-6 h-full">
            {renderSubView()}
        </div>
    );
};

export default StrategyView;
