import React, { useState } from 'react';
import Button from './ui/Button';
import { RocketIcon } from './icons/RocketIcon';
import { LayoutGridIcon } from './icons/LayoutGridIcon';
import { FileTextIcon } from './icons/FileTextIcon';
import DashboardChart from './ui/DashboardChart';
import { CheckCircleIcon } from './icons/CheckCircleIcon';
import { HeartIcon } from './icons/HeartIcon';
import { CommentIcon } from './icons/CommentIcon';
import { UsersIcon } from './icons/UsersIcon';
import { TrendingUpIcon } from './icons/TrendingUpIcon';
import PlatformIcon from './ui/PlatformIcon';
import Countdown from './ui/Countdown';
import AdvancedStatCard from './ui/AdvancedStatCard';
import Tabs from './ui/Tabs';
import ContentPipeline from './ContentPipeline';
import AIInsights from './AIInsights';
import { useSettings, usePosts, useUI } from '../contexts/AppContext';
import { supabase } from '../services/supabaseClient';
import { Post } from '../types';
import { format, parseISO } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';


const PostListItem: React.FC<{post: Post; onClick: () => void}> = ({ post, onClick }) => (
    <li className="flex items-center space-x-4 py-3 cursor-pointer hover:bg-slate-50" onClick={onClick}>
        <div className="flex-shrink-0">
            {post.imgSrc ? 
                <img src={post.imgSrc} alt="Post thumbnail" className="w-12 h-12 rounded-md object-cover bg-slate-200"/> :
                <div className="w-12 h-12 rounded-md bg-slate-200 flex items-center justify-center">
                    <PlatformIcon platform={post.platform} className="w-6 h-6 text-slate-400"/>
                </div>
            }
        </div>
        <div className="flex-grow min-w-0">
            <p className="text-sm font-semibold text-slate-800 truncate">{post.caption}</p>
            {post.status === 'published' ? (
                <div className="flex items-center text-xs text-green-600 font-semibold mt-1">
                    <CheckCircleIcon className="w-4 h-4 mr-1.5" />
                    Published
                </div>
            ) : (
                <p className="text-xs text-slate-500">{format(toZonedTime(parseISO(post.scheduledDate!), Intl.DateTimeFormat().resolvedOptions().timeZone), 'p')}</p>
            )}
        </div>
        <div className="flex-shrink-0">
            <PlatformIcon platform={post.platform} className="w-5 h-5 text-slate-500"/>
        </div>
    </li>
);

const Dashboard: React.FC = () => {
    const { settings } = useSettings();
    const { posts } = usePosts();
    const { setView, addToast } = useUI();
    const [isSchedulerRunning, setIsSchedulerRunning] = useState(false);
    const [dateRange, setDateRange] = useState('last7days');

    const handleRunScheduler = async () => {
        setIsSchedulerRunning(true);
        addToast('Manual scheduler run initiated...', 'info');
        try {
            const { error } = await supabase.functions.invoke('scheduler');
            if (error) throw error;
            addToast('Scheduler run completed successfully!', 'success');
        } catch (error: any) {
            addToast(`Scheduler run failed: ${error.message}`, 'error');
        } finally {
            setIsSchedulerRunning(false);
        }
    };
    
    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const todayEnd = new Date(todayStart.getTime());
    todayEnd.setDate(todayEnd.getDate() + 1);

    const endOfWeek = new Date(todayStart);
    endOfWeek.setDate(todayStart.getDate() + (7 - todayStart.getDay()) % 7);
    endOfWeek.setHours(23, 59, 59, 999);

    const endOfNextWeek = new Date(endOfWeek);
    endOfNextWeek.setDate(endOfWeek.getDate() + 7);
    
    const todaysPosts = posts
        .filter(p => p.scheduledDate && new Date(p.scheduledDate) >= todayStart && new Date(p.scheduledDate) < todayEnd)
        .sort((a,b) => new Date(a.scheduledDate!).getTime() - new Date(b.scheduledDate!).getTime());

    const upcomingThisWeekPosts = posts
        .filter(p => p.scheduledDate && new Date(p.scheduledDate) >= todayEnd && new Date(p.scheduledDate) <= endOfWeek)
        .sort((a,b) => new Date(a.scheduledDate!).getTime() - new Date(b.scheduledDate!).getTime());

    const hasContentForNext7Days = posts.some(p => p.scheduledDate && new Date(p.scheduledDate) > now && new Date(p.scheduledDate) <= endOfNextWeek);

    const nextPostsByPlatform = settings.platforms.map(platform => {
        return posts
            .filter(p => p.platform === platform && p.status === 'scheduled' && new Date(p.scheduledDate!) > now)
            .sort((a, b) => new Date(a.scheduledDate!).getTime() - new Date(b.scheduledDate!).getTime())[0];
    }).filter(Boolean);

    const getGreeting = () => {
        const hour = new Date().getHours();
        if (hour < 12) return "Good morning";
        if (hour < 18) return "Good afternoon";
        return "Good evening";
    };

    const weeklyData = posts.reduce((acc, post) => {
        if (!post.scheduledDate) return acc;
        const week = format(parseISO(post.scheduledDate), 'yyyy-ww');
        if (!acc[week]) {
            acc[week] = { name: `Week ${format(parseISO(post.scheduledDate), 'ww')}`, posts: 0, likes: 0, comments: 0 };
        }
        acc[week].posts++;
        acc[week].likes += post.likes;
        acc[week].comments += post.comments;
        return acc;
    }, {} as Record<string, { name: string; posts: number; likes: number; comments: number }>);

    const chartData = Object.values(weeklyData);

    const layout = [
        { i: 'a', x: 0, y: 0, w: 4, h: 2 },
        { i: 'b', x: 4, y: 0, w: 4, h: 2 },
        { i: 'c', x: 8, y: 0, w: 4, h: 2 },
        { i: 'd', x: 0, y: 2, w: 6, h: 4 },
        { i: 'e', x: 6, y: 2, w: 6, h: 4 },
    ];

    return (
        <div className="space-y-6 lg:space-y-8">
            {/* Mobile Header */}
            <div className="lg:hidden">
                <h2 className="text-2xl font-bold text-slate-800 mb-2">
                    {getGreeting()}, {settings.brandName || "Strategist"}.
                </h2>
                <p className="text-slate-500 text-sm mb-4">Welcome to PulseCraft! Here's your dashboard.</p>
                <div className="flex flex-col sm:flex-row gap-3 sm:items-center sm:justify-between">
                    <select
                        value={dateRange}
                        onChange={(e) => setDateRange(e.target.value)}
                        className="bg-white border border-slate-200 rounded-md shadow-sm px-3 py-2 text-sm font-medium text-slate-700 hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 min-h-[44px]"
                    >
                        <option value="last7days">Last 7 Days</option>
                        <option value="last30days">Last 30 Days</option>
                        <option value="thismonth">This Month</option>
                    </select>
                    <button
                        onClick={() => window.open('https://discord.gg/your-invite-link', '_blank')}
                        className="p-3 rounded-full bg-slate-100 text-slate-500 hover:bg-slate-200 hover:text-slate-900 transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center self-start sm:self-auto"
                        title="Join Discord Community"
                    >
                        <UsersIcon className="w-5 h-5" />
                    </button>
                </div>
            </div>

            {/* Desktop Header */}
            <div className="hidden lg:flex justify-between items-center">
                <div>
                    <h2 className="text-3xl font-bold text-slate-800">{getGreeting()}, {settings.brandName || "Strategist"}.</h2>
                    <p className="text-slate-500 mt-1">Welcome to PulseCraft! Here's your co-pilot dashboard at a glance.</p>
                </div>
                <div className="flex items-center space-x-2">
                    <select
                        value={dateRange}
                        onChange={(e) => setDateRange(e.target.value)}
                        className="bg-white border border-slate-200 rounded-md shadow-sm px-3 py-2 text-sm font-medium text-slate-700 hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                    >
                        <option value="last7days">Last 7 Days</option>
                        <option value="last30days">Last 30 Days</option>
                        <option value="thismonth">This Month</option>
                    </select>
                    <button onClick={() => window.open('https://discord.gg/your-invite-link', '_blank')} className="p-2 rounded-full bg-slate-100 text-slate-500 hover:bg-slate-200 hover:text-slate-900 transition-colors">
                        <UsersIcon className="w-6 h-6" />
                    </button>
                </div>
            </div>

            <Tabs tabs={['Overview', 'Analytics', 'Gamification']}>
                <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <AdvancedStatCard 
                            title="Engagement Rate"
                            value="2.5%"
                            percentageChange={15}
                            data={[5, 10, 15, 12, 18, 20, 25]}
                            icon={<TrendingUpIcon className="w-6 h-6"/>}
                        />
                        <AdvancedStatCard 
                            title="Impressions/Reach"
                            value="12.5k"
                            percentageChange={-5}
                            data={[30, 25, 28, 22, 20, 18, 15]}
                            icon={<UsersIcon className="w-6 h-6"/>}
                        />
                        <AdvancedStatCard 
                            title="New Followers"
                            value="256"
                            percentageChange={8}
                            data={[10, 12, 8, 15, 18, 20, 22]}
                            icon={<UsersIcon className="w-6 h-6"/>}
                        />
                    </div>

                    {!hasContentForNext7Days && posts.length > 0 && (
                        <div className="p-4 bg-amber-50 border-l-4 border-amber-400 text-amber-800">
                            <p className="font-bold">Plan Ahead!</p>
                            <p className="text-sm">You have no content scheduled for the next 7 days. Go to the "Strategy Brief" to generate more posts.</p>
                        </div>
                    )}
                    
                    <ContentPipeline 
                        todaysPosts={todaysPosts}
                        upcomingThisWeekPosts={upcomingThisWeekPosts}
                        nextPostsByPlatform={nextPostsByPlatform}
                    />
                </div>
                <div>
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div className="bg-white p-4 rounded-lg border border-slate-200 shadow-sm">
                            <h3 className="font-bold text-slate-800 mb-4">Posts per Week</h3>
                            <DashboardChart data={chartData} chartType="bar" dataKey="posts" />
                        </div>
                        <div className="bg-white p-4 rounded-lg border border-slate-200 shadow-sm">
                            <h3 className="font-bold text-slate-800 mb-4">Engagement per Week</h3>
                            <DashboardChart data={chartData} chartType="line" dataKey="likes" />
                        </div>
                    </div>
                </div>
                <div className="space-y-6">
                    <AIInsights />
                    <div className="bg-white p-4 rounded-lg border border-slate-200 shadow-sm">
                        <div className="flex items-center justify-between">
                            <div>
                                <h3 className="font-bold text-slate-800">Automatic Scheduler</h3>
                                <p className="text-sm text-slate-500">This runs every 15 minutes to publish scheduled posts.</p>
                            </div>
                            <Button onClick={handleRunScheduler} disabled={isSchedulerRunning}>
                                {isSchedulerRunning ? 'Running...' : 'Run Now'}
                            </Button>
                        </div>
                    </div>
                </div>
            </Tabs>
        </div>
    );
};

export default Dashboard;
