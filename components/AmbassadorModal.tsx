import React, { useState } from 'react';
import Modal from './ui/Modal';
import Button from './ui/Button';
import { GiftIcon } from './icons/GiftIcon';
import { CopyIcon } from './icons/CopyIcon';
import { useSettings, useUI } from '../contexts/AppContext';

const AmbassadorModal: React.FC = () => {
    const { settings, handleApplyPromoCode } = useSettings();
    const { setAmbassadorModalOpen } = useUI();
    
    const [codeCopied, setCodeCopied] = useState(false);
    const [linkCopied, setLinkCopied] = useState(false);
    
    const referralLink = `https://pulsecraft.space/join?ref=${btoa(settings.userId)}`;
    const promoCode = 'PULSECRAFTPIONEER';

    const handleCopy = (textToCopy: string, type: 'code' | 'link') => {
        navigator.clipboard.writeText(textToCopy);
        if (type === 'code') { setCodeCopied(true); setTimeout(() => setCodeCopied(false), 2000); } 
        else { setLinkCopied(true); setTimeout(() => setLinkCopied(false), 2000); }
    };

    const handleRedeem = () => {
        handleApplyPromoCode(promoCode);
        setAmbassadorModalOpen(false);
    };
    
    return (
        <Modal isOpen={true} onClose={() => setAmbassadorModalOpen(false)} title="A Thank You For Our Pioneers" size="2xl">
            <div className="text-center">
                <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-purple-100 mb-5">
                    <GiftIcon className="h-8 w-8 text-purple-600" />
                </div>
                <h2 className="text-2xl font-bold text-slate-800">You're making a huge difference!</h2>
                <p className="mt-2 text-slate-600">As a thank you for being an invaluable early tester, we've got two special offers just for you.</p>
            </div>

            <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
                <div className="bg-slate-50 p-6 rounded-lg border border-slate-200">
                    <h3 className="font-bold text-lg text-slate-800">1. Credit Boost</h3>
                    <p className="text-sm text-slate-500 mt-1">Don't let credits stop you. Use the code below for a 5x boost (2,500 credits) on us!</p>
                    <div className="mt-4 flex items-center space-x-2">
                        <input type="text" readOnly value={promoCode} className="input font-mono text-sm tracking-widest flex-grow" />
                        <Button variant="outline" size="sm" onClick={() => handleCopy(promoCode, 'code')}>
                            <CopyIcon className="w-4 h-4"/>
                            <span className={`ml-1 transition-opacity ${codeCopied ? 'opacity-100' : 'opacity-0'}`}>Copied!</span>
                        </Button>
                    </div>
                    <Button onClick={handleRedeem} variant="primary" size="sm" className="w-full mt-3">Redeem Credits Now</Button>
                </div>

                <div className="bg-slate-50 p-6 rounded-lg border border-slate-200">
                    <h3 className="font-bold text-lg text-slate-800">2. Become an Ambassador</h3>
                    <p className="text-sm text-slate-500 mt-1">Love PulseCraft? Share it! You'll get more credits and other perks for every user who joins through your link.</p>
                    <div className="mt-4 flex items-center space-x-2">
                         <input type="text" readOnly value={referralLink} className="input text-xs flex-grow" />
                         <Button variant="outline" size="sm" onClick={() => handleCopy(referralLink, 'link')}>
                            <CopyIcon className="w-4 h-4"/>
                            <span className={`ml-1 transition-opacity ${linkCopied ? 'opacity-100' : 'opacity-0'}`}>Copied!</span>
                        </Button>
                    </div>
                     <p className="text-xs text-slate-400 mt-3">We believe in building this community with you. This is our honest way of growing together.</p>
                </div>
            </div>
             <style>{`
                .input { display: block; width: 100%; border-radius: 0.375rem; border: 1px solid #e2e8f0; padding: 0.5rem 0.75rem; background-color: white; line-height: 1.5; color: #0f172a; }
                .input:focus { outline: 2px solid transparent; outline-offset: 2px; border-color: #9333ea; box-shadow: 0 0 0 2px #e9d5ff; }
            `}</style>
        </Modal>
    );
};

export default AmbassadorModal;