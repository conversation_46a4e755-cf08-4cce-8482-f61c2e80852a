import React, { useState } from 'react';
import Modal from './ui/Modal';
import Button from './ui/Button';
import Loader from './ui/Loader';
import { useApp, useUI } from '../contexts/AppContext';

interface FeedbackModalProps {
    isSubmitting: boolean;
}

const FeedbackModal: React.FC<FeedbackModalProps> = ({ isSubmitting }) => {
    const { handleSubmitFeedback } = useApp();
    const { view, setFeedbackModalOpen } = useUI();
    const [feedbackType, setFeedbackType] = useState('Feature Request');
    const [mood, setMood] = useState('Happy');
    const [message, setMessage] = useState('');

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!message.trim() || isSubmitting) return;
        handleSubmitFeedback(`${feedbackType} (Mood: ${mood})`, message);
    };

    return (
        <Modal isOpen={true} onClose={() => setFeedbackModalOpen(false)} title="Submit Feedback" size="xl">
            <form onSubmit={handleSubmit} className="flex flex-col h-full">
                <p className="text-sm text-slate-600 mb-6">
                    Your feedback is crucial for improving the Co-pilot. Thank you! Your current view ('{view}') will be included for context.
                </p>
                <div className="flex-grow space-y-4">
                    <div>
                        <label htmlFor="feedbackType" className="label">Feedback Type</label>
                        <select id="feedbackType" value={feedbackType} onChange={(e) => setFeedbackType(e.target.value)} className="input" disabled={isSubmitting}>
                            <option>Feature Request</option>
                            <option>Bug Report</option>
                            <option>General Feedback</option>
                            <option>Other</option>
                        </select>
                    </div>
                    <div>
                        <label htmlFor="mood" className="label">How are you feeling?</label>
                        <select id="mood" value={mood} onChange={(e) => setMood(e.target.value)} className="input" disabled={isSubmitting}>
                            <option>Happy</option>
                            <option>Neutral</option>
                            <option>Sad</option>
                        </select>
                    </div>
                    <div>
                        <label htmlFor="message" className="label">Message</label>
                        <textarea id="message" value={message} onChange={(e) => setMessage(e.target.value)} className="input h-40" placeholder={`e.g., "It would be great if..."`} required disabled={isSubmitting} />
                    </div>
                </div>
                <div className="flex justify-end space-x-2 mt-6 pt-4 border-t -mx-6 -mb-6 px-6 pb-4 bg-slate-50 rounded-b-xl">
                    <Button type="button" onClick={() => setFeedbackModalOpen(false)} variant="outline" disabled={isSubmitting}>Cancel</Button>
                    <Button type="submit" variant="primary" disabled={isSubmitting || !message.trim()}>
                        {isSubmitting ? <><Loader size="sm" className="mr-2" />Submitting...</> : 'Submit Feedback'}
                    </Button>
                </div>
                <style>{`.label { display: block; font-weight: 600; font-size: 0.875rem; color: #334155; margin-bottom: 0.5rem; } .input { display: block; width: 100%; border-radius: 0.375rem; border: 1px solid #e2e8f0; padding: 0.5rem 0.75rem; background-color: white; line-height: 1.5; color: #0f172a; } .input:disabled { background-color: #f1f5f9; cursor: not-allowed; } .input:focus { outline: 2px solid transparent; outline-offset: 2px; border-color: #4f46e5; box-shadow: 0 0 0 2px #c7d2fe; }`}</style>
            </form>
        </Modal>
    );
};

export default FeedbackModal;
