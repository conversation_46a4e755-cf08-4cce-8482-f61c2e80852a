import React, { useMemo, useState, useCallback } from 'react';
import { WeekData } from '../types';
import Button from './ui/Button';
import Loader from './ui/Loader';
import { WandIcon } from './icons/WandIcon';
import { CheckCircleIcon } from './icons/CheckCircleIcon';
import Accordion from './ui/Accordion';
import ConfirmationModal from './ui/ConfirmationModal';
import { COSTS } from '../contexts/AppContext';
import { parsePlanToHierarchy } from '../utils/planParser';
import { usePlan, useSettings } from '../contexts/AppContext';


declare const marked: any;

const WeeklyBatchButton: React.FC<{ 
    weekId: string; 
    weekData: WeekData;
    onGenerate: () => Promise<void>;
}> = ({ weekId, weekData, onGenerate }) => {
    const { settings, loadingStates } = useSettings();
    const isGenerating = loadingStates.weeklyBatch === weekId;
    
    const handleGenerate = async () => {
        try {
            await onGenerate();
        } catch (error) {
            console.error("Batch generation failed for week:", weekId, error);
        }
    };
    
    const canGenerate = settings.role === 'admin' || settings.credits >= COSTS.WEEKLY_POST_BATCH;
    const totalPostsToGenerate = weekData.postIdeas.length * settings.platforms.length;
    const disabledTitle = !canGenerate ? `Insufficient credits. Needs ${COSTS.WEEKLY_POST_BATCH}.` : `Generate ${totalPostsToGenerate} posts for ${COSTS.WEEKLY_POST_BATCH} credits.`;

    if (isGenerating) return <div className="flex items-center text-xs text-slate-500"><Loader size="sm" className="mr-2"/>Generating...</div>;

    return <Button onClick={handleGenerate} size="xs" variant="primary" disabled={isGenerating || !canGenerate} title={disabledTitle}>Generate {totalPostsToGenerate} Posts</Button>;
};

const WeekComponent: React.FC<{ 
    weekId: string; 
    weekName: string; 
    weekData: WeekData; 
    isCompleted: boolean; 
    flatWeekIds: string[]; 
}> = (props) => {
    const { weekId, weekName, weekData, isCompleted, flatWeekIds } = props;
    const { handleGenerateWeeklyBatch, handlePlanUpdate, planMarkdown, completedWeeks } = usePlan();
    const { loadingStates } = useSettings();
    const [instructions, setInstructions] = useState('');
    const [newPostIdea, setNewPostIdea] = useState('');
    const isGenerating = loadingStates.weeklyBatch === weekId;

    const statusIndicator = {
        idle: <div className="w-3 h-3 bg-slate-300 rounded-full" title="Not Started"></div>,
        success: <CheckCircleIcon className="w-5 h-5 text-green-500" title="Completed"/>,
    };
    
    return (
        <div className={`p-4 rounded-lg border transition-shadow hover:shadow-md flex flex-col ${isCompleted ? 'bg-green-50 border-green-200' : 'bg-white border-slate-200'}`}>
            <div className="flex justify-between items-start">
                <h5 className="font-bold text-slate-800 mb-2">{weekName}</h5>
                <div className="flex items-center space-x-2">
                    {statusIndicator[isCompleted ? 'success' : 'idle']}
                    {!isCompleted && <Button onClick={() => {
                        const newCompletedWeeks = [...completedWeeks, weekId];
                        handlePlanUpdate(planMarkdown.replace(`"completedWeeks":${JSON.stringify(completedWeeks)}`, `"completedWeeks":${JSON.stringify(newCompletedWeeks)}`));
                    }} size="xs" variant="outline">Mark as Complete</Button>}
                </div>
            </div>
            <div className="flex-grow space-y-2">
                <p className="text-xs text-slate-500"><strong className="text-slate-600">Strategy:</strong> {weekData.strategy}</p>
                <details className="text-xs"><summary className="cursor-pointer text-slate-500 hover:text-slate-800">More details</summary><div className="mt-2 space-y-1 text-slate-600">
                    {weekData.audienceSegment && <p><strong className="text-slate-700">Audience Segment:</strong> {weekData.audienceSegment}</p>}
                    <p><strong className="text-slate-700">Story Idea:</strong> {weekData.story}</p>
                    {weekData.communityEngagement && <p><strong className="text-slate-700">Community Engagement:</strong> {weekData.communityEngagement}</p>}
                    <p className="text-slate-700 font-semibold mt-2 mb-1">Core Concepts:</p>
                    {weekData.postIdeas.length > 0 ? <ul className="list-disc pl-4 text-slate-600 space-y-1">{weekData.postIdeas.map((i, idx) => <li key={idx}><span>{i.concept} ({i.format})</span></li>)}</ul> : <div className="p-2 bg-slate-50 border-l-2 border-slate-300"><p className="text-slate-500 italic">No specific post ideas generated.</p></div>}
                    <div className="mt-2">
                        <div className="flex items-center space-x-2">
                            <input type="text" value={newPostIdea} onChange={(e) => setNewPostIdea(e.target.value)} className="input w-full text-xs" placeholder="Add a new post idea..." />
                            <Button onClick={() => {
                                const newPostIdeas = [...weekData.postIdeas, { concept: newPostIdea, format: 'Static Image' }];
                                const newWeekData = { ...weekData, postIdeas: newPostIdeas };
                                const newPlan = planMarkdown.replace(JSON.stringify(weekData), JSON.stringify(newWeekData));
                                handlePlanUpdate(newPlan);
                                setNewPostIdea('');
                            }} size="xs" variant="outline" className="mt-2">Add</Button>
                        </div>
                    </div>
                    <div className="mt-4">
                        <h4 className="text-slate-700 font-semibold mb-2">Advanced Post Types</h4>
                        <div className="flex items-center space-x-2">
                            <Button size="xs" variant="outline" disabled>Stories (Coming Soon)</Button>
                            <Button size="xs" variant="outline" disabled>Carousels (Coming Soon)</Button>
                        </div>
                    </div>
                </div></details>
            </div>
            <div className="mt-3">
                {!isCompleted && weekData.postIdeas.length > 0 && (
                    <div className="space-y-2">
                         <textarea value={instructions} onChange={(e) => setInstructions(e.target.value)} className="input w-full text-xs" placeholder="Add specific instructions (optional)..." rows={2} disabled={isGenerating}/>
                        <div className="text-right"><WeeklyBatchButton weekId={weekId} weekData={weekData} onGenerate={() => handleGenerateWeeklyBatch(weekId, weekData, flatWeekIds, instructions)}/></div>
                    </div>
                )}
                {!isCompleted && weekData.postIdeas.length === 0 && <p className="text-xs text-slate-400 italic text-right">No post ideas to generate.</p>}
            </div>
            <style>{`.input { display: block; width: 100%; background-color: var(--card); box-shadow: inset 0 1px 2px 0 rgb(0 0 0 / 0.05); border-radius: 0.375rem; border: 1px solid var(--border); padding: 0.5rem 0.75rem; line-height: 1.5; color: var(--foreground); } .input:focus { outline: 2px solid transparent; outline-offset: 2px; border-color: var(--ring); box-shadow: 0 0 0 2px #e9d5ff; }`}</style>
        </div>
    );
};


const BriefView: React.FC = () => {
    const { planMarkdown, handlePlanUpdate, handleRefinePlan, completedWeeks } = usePlan();
    const { settings, loadingStates } = useSettings();
    const hierarchy = useMemo(() => parsePlanToHierarchy(planMarkdown), [planMarkdown]);
    const [refiningSection, setRefiningSection] = useState<string | null>(null);
    const [localRefinementInput, setLocalRefinementInput] = useState("");
    const [pendingPlanUpdate, setPendingPlanUpdate] = useState<{ newPlan: string; diff: string } | null>(null);

    const flatWeekIds = useMemo(() => Object.values(hierarchy).flatMap(y => Object.values(y)).flatMap(q => Object.values(q)).flatMap(m => Object.keys(m)), [hierarchy]);

    const handleRefineClick = (sectionTitle: string) => {
        setRefiningSection(refiningSection === sectionTitle ? null : sectionTitle);
        setLocalRefinementInput("");
    };
    
    const handleRefine = useCallback(async (sectionContext: string) => {
        if (!localRefinementInput.trim()) return;
        const result = await handleRefinePlan(`Refine the section: "${sectionContext}". User Request: "${localRefinementInput}"`);
        if (result) {
            setPendingPlanUpdate(result);
            setLocalRefinementInput('');
            setRefiningSection(null);
        }
    }, [localRefinementInput, handleRefinePlan]);

    const handleConfirmRefinement = () => {
        if (pendingPlanUpdate) handlePlanUpdate(pendingPlanUpdate.newPlan);
        setPendingPlanUpdate(null);
    };

    const canRefine = settings.role === 'admin' || settings.credits >= COSTS.PLAN_REFINEMENT;
    const refineDisabledTitle = !canRefine ? `Insufficient credits. Needs ${COSTS.PLAN_REFINEMENT}.` : 'Refine the plan';

    const RefinementInput: React.FC<{ sectionTitle: string }> = ({ sectionTitle }) => {
        if (refiningSection !== sectionTitle) return null;
        return (
            <div className="p-4 mb-3 bg-slate-50/80 border border-slate-200 rounded-lg animate-fade-in">
                <div className="flex items-center text-sm font-bold text-slate-700 mb-2"><WandIcon className="w-5 h-5 mr-2 text-purple-500"/><span>Refine '{sectionTitle}'</span></div>
                <div className="flex space-x-2">
                    <textarea value={localRefinementInput} onChange={(e) => setLocalRefinementInput(e.target.value)} onKeyDown={(e) => { if (e.key === 'Enter' && !e.shiftKey) { e.preventDefault(); !loadingStates.refine && handleRefine(sectionTitle); } }} className={`input w-full min-h-[40px] max-h-48 resize-y ${loadingStates.refine ? 'thinking-glow' : ''}`} placeholder="e.g., Make this more aggressive..." disabled={loadingStates.refine || !canRefine} rows={2} autoFocus />
                    <Button onClick={() => handleRefine(sectionTitle)} variant="accent" disabled={loadingStates.refine || !localRefinementInput.trim() || !canRefine} title={refineDisabledTitle}>{loadingStates.refine ? <Loader size="sm"/> : `Refine (${COSTS.PLAN_REFINEMENT} cr)`}</Button>
                </div>
                <style>{`@keyframes fade-in { from { opacity: 0; } to { opacity: 1; } } .animate-fade-in { animation: fade-in 0.3s ease-out; }`}</style>
            </div>
        );
    };

    return (
        <div className="h-full flex flex-col relative -mx-6 -my-6">
            <div className="px-6 py-4 flex-grow overflow-y-auto modal-content">
                <p className="mb-6 text-slate-600">This is your actionable brief. Generate content for each week, or use the Co-pilot to refine any part of the plan.</p>
                <div className="space-y-4">
                {Object.entries(hierarchy).map(([yearName, yearData], yearIndex) => (
                    <Accordion key={yearName} title={yearName} initiallyOpen={yearIndex === 0} actionComponent={<Button variant="ghost" size="xs" onClick={() => handleRefineClick(yearName)}>Refine</Button>}>
                        <RefinementInput sectionTitle={yearName} />
                        <div className="space-y-3">
                        {Object.entries(yearData).map(([quarterName, quarterData]) => {
                            const weeksInQuarter = Object.values(quarterData).flatMap(monthData => Object.keys(monthData));
                            const completedInQuarter = weeksInQuarter.filter(weekId => completedWeeks.includes(weekId)).length;
                            const progress = weeksInQuarter.length > 0 ? (completedInQuarter / weeksInQuarter.length) * 100 : 0;
                            return (
                                <Accordion key={quarterName} title={quarterName} initiallyOpen={yearIndex === 0 && quarterName.includes('Q1')} badge={<div className="w-24 bg-slate-200 rounded-full h-2.5 ml-3" title={`${Math.round(progress)}% Complete`}><div className="bg-green-500 h-2.5 rounded-full" style={{ width: `${progress}%` }}></div></div>} actionComponent={<Button variant="ghost" size="xs" onClick={() => handleRefineClick(`${yearName} - ${quarterName}`)}>Refine</Button>}>
                                    <RefinementInput sectionTitle={`${yearName} - ${quarterName}`} />
                                    <div className="space-y-2">
                                    {Object.entries(quarterData).map(([monthName, monthData]) => (
                                        <div key={monthName}>
                                            <h4 className="text-md font-semibold text-slate-700 mt-2 mb-2 ml-1">{monthName.split(':')[0]}</h4>
                                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                                            {Object.entries(monthData).map(([weekId, weekData]) => {
                                                const weekName = weekId.split(' | ').pop() || weekId;
                                                return <WeekComponent key={weekId} weekId={weekId} weekName={weekName} weekData={weekData} isCompleted={completedWeeks.includes(weekId)} flatWeekIds={flatWeekIds} />;
                                            })}
                                            </div>
                                        </div>
                                    ))}
                                    </div>
                                </Accordion>
                            );
                        })}
                        </div>
                    </Accordion>
                ))}
                </div>
            </div>
            
            {pendingPlanUpdate && (<ConfirmationModal isOpen={true} onClose={() => setPendingPlanUpdate(null)} onConfirm={handleConfirmRefinement} title="Confirm Plan Refinement" confirmText="Accept Changes" size="3xl"><p className="text-sm text-slate-600">The AI has suggested the following changes. Review and confirm to update your plan.</p><div className="mt-4 p-4 bg-slate-50 border rounded-md max-h-80 overflow-y-auto prose prose-sm max-w-none" dangerouslySetInnerHTML={{ __html: marked.parse(pendingPlanUpdate.diff) }}/></ConfirmationModal>)}
             <style>{`.input { display: block; width: 100%; background-color: var(--card); box-shadow: inset 0 1px 2px 0 rgb(0 0 0 / 0.05); border-radius: 0.375rem; border: 1px solid var(--border); padding: 0.5rem 0.75rem; } .input:focus { outline: 2px solid transparent; outline-offset: 2px; border-color: var(--ring); }`}</style>
        </div>
    );
};

export default BriefView;
