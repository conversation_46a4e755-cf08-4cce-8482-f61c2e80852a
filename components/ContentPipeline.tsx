import React, { useState } from 'react';
import { Post } from '../types';
import PostListItem from './PostListItem';
import Button from './ui/Button';
import { CalendarIcon } from './icons/CalendarIcon';
import { ListIcon } from './icons/ListIcon';
import ComingSoonMarker from './ui/ComingSoonMarker';
import PlatformIcon from './ui/PlatformIcon';
import Countdown from './ui/Countdown';

interface ContentPipelineProps {
  todaysPosts: Post[];
  upcomingThisWeekPosts: Post[];
  nextPostsByPlatform: Post[];
}

const ContentPipeline: React.FC<ContentPipelineProps> = ({ todaysPosts, upcomingThisWeekPosts, nextPostsByPlatform }) => {
  const [view, setView] = useState('list');

  return (
    <div className="relative bg-white p-4 rounded-lg border border-slate-200 shadow-sm">
      <ComingSoonMarker />
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-bold text-slate-800">Content Pipeline</h3>
        <div className="flex items-center space-x-2">
          <Button variant={view === 'list' ? 'primary' : 'secondary'} size="sm" onClick={() => setView('list')}>
            <ListIcon className="w-4 h-4 mr-2" />
            List
          </Button>
          <Button variant={view === 'calendar' ? 'primary' : 'secondary'} size="sm" onClick={() => setView('calendar')}>
            <CalendarIcon className="w-4 h-4 mr-2" />
            Calendar
          </Button>
        </div>
      </div>

      {view === 'list' ? (
        <div className="space-y-6">
          {nextPostsByPlatform.length > 0 && (
            <div className="space-y-4">
              <h4 className="text-lg font-bold text-slate-800">Next Up</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {nextPostsByPlatform.map(post => (
                  <div key={post.id} className="bg-white p-4 rounded-lg border border-slate-200 shadow-sm">
                    <div className="flex items-center justify-between mb-2">
                      <PlatformIcon platform={post.platform} className="w-6 h-6 text-slate-600" />
                      <Countdown targetDate={post.scheduledDate!} />
                    </div>
                    <p className="text-sm text-slate-600 truncate">{post.caption}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="space-y-4">
            <h4 className="text-lg font-bold text-slate-800">Today's Schedule</h4>
            {todaysPosts.length > 0 ? (
              <ul className="divide-y divide-slate-100">
                {todaysPosts.map(post => <PostListItem key={post.id} post={post} onClick={() => {}} />)}
              </ul>
            ) : (
              <div className="text-center py-8 text-slate-500">
                <p>Nothing scheduled for today. Relax!</p>
              </div>
            )}
          </div>

          <div className="space-y-4">
            <h4 className="text-lg font-bold text-slate-800">Coming Up This Week</h4>
            {upcomingThisWeekPosts.length > 0 ? (
              <ul className="divide-y divide-slate-100">
                {upcomingThisWeekPosts.map(post => <PostListItem key={post.id} post={post} onClick={() => {}} />)}
              </ul>
            ) : (
              <div className="text-center py-8 text-slate-500">
                <p>No more posts scheduled this week.</p>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="text-center py-8 text-slate-500">
          <p>Calendar view coming soon!</p>
        </div>
      )}
    </div>
  );
};

export default ContentPipeline;
