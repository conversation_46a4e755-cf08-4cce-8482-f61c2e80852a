import React, { useState, useMemo } from 'react';
import { Post } from '../types';
import PlatformIcon from './ui/PlatformIcon';
import Button from './ui/Button';
import { ChevronDownIcon } from './icons/ChevronDownIcon';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isToday, addMonths, subMonths } from 'date-fns';

interface CalendarViewProps {
    posts: Post[];
    onPostClick: (post: Post) => void;
}

const CalendarView: React.FC<CalendarViewProps> = ({ posts, onPostClick }) => {
    const [currentDate, setCurrentDate] = useState(new Date());

    const nextMonth = () => setCurrentDate(addMonths(currentDate, 1));
    const prevMonth = () => setCurrentDate(subMonths(currentDate, 1));
    const goToToday = () => setCurrentDate(new Date());

    // Get all days in the current month
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);
    const daysInMonth = eachDayOfInterval({ start: monthStart, end: monthEnd });

    // Group posts by date
    const postsByDate = useMemo(() => {
        const grouped: { [key: string]: Post[] } = {};
        posts.forEach(post => {
            const postDate = new Date(post.scheduledDate || post.date);
            const dateKey = format(postDate, 'yyyy-MM-dd');
            if (!grouped[dateKey]) grouped[dateKey] = [];
            grouped[dateKey].push(post);
        });
        return grouped;
    }, [posts]);

    return (
        <div className="h-full flex flex-col">
            {/* Calendar Header */}
            <div className="flex items-center justify-between mb-6 bg-white p-4 rounded-lg border border-slate-200">
                <div className="flex items-center space-x-4">
                    <h2 className="text-xl font-bold text-slate-800">
                        {format(currentDate, 'MMMM yyyy')}
                    </h2>
                    <Button onClick={goToToday} variant="outline" size="sm">
                        Today
                    </Button>
                </div>
                <div className="flex items-center space-x-2">
                    <Button onClick={prevMonth} variant="ghost" size="sm" className="p-2">
                        <ChevronDownIcon className="w-4 h-4 rotate-90" />
                    </Button>
                    <Button onClick={nextMonth} variant="ghost" size="sm" className="p-2">
                        <ChevronDownIcon className="w-4 h-4 -rotate-90" />
                    </Button>
                </div>
            </div>

            {/* Calendar Grid */}
            <div className="flex-1 bg-white rounded-lg border border-slate-200 overflow-hidden">
                <div className="grid grid-cols-7 gap-0">
                    {/* Day Headers */}
                    {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                        <div key={day} className="p-3 text-center font-semibold text-slate-600 bg-slate-50 border-b border-slate-200 text-sm">
                            {day}
                        </div>
                    ))}
                    
                    {/* Calendar Days */}
                    {daysInMonth.map((day, index) => {
                        const dateKey = format(day, 'yyyy-MM-dd');
                        const dayPosts = postsByDate[dateKey] || [];
                        const isCurrentDay = isToday(day);

                        return (
                            <div
                                key={index}
                                className={`p-2 border-b border-r border-slate-200 min-h-[120px] hover:bg-slate-50 transition-colors ${
                                    isCurrentDay ? 'bg-purple-50 ring-1 ring-purple-200' : 'bg-white'
                                }`}
                            >
                                <div className={`text-sm font-medium mb-2 ${
                                    isCurrentDay ? 'text-purple-600' : 'text-slate-800'
                                }`}>
                                    {format(day, 'd')}
                                </div>
                                
                                <div className="space-y-1">
                                    {dayPosts.slice(0, 3).map(post => (
                                        <div
                                            key={post.id}
                                            className={`text-xs p-1.5 rounded cursor-pointer transition-all duration-200 hover:shadow-sm ${
                                                post.status === 'published' 
                                                    ? 'bg-green-100 text-green-800 border border-green-200' 
                                                    : post.status === 'scheduled' 
                                                    ? 'bg-blue-100 text-blue-800 border border-blue-200' 
                                                    : 'bg-slate-100 text-slate-800 border border-slate-200'
                                            }`}
                                            onClick={() => onPostClick(post)}
                                        >
                                            <div className="flex items-center space-x-1">
                                                <PlatformIcon platform={post.platform} className="w-3 h-3 flex-shrink-0" />
                                                <span className="truncate font-medium">
                                                    {post.caption.slice(0, 25)}...
                                                </span>
                                            </div>
                                            {post.scheduledDate && (
                                                <div className="text-xs opacity-75 mt-0.5">
                                                    {format(new Date(post.scheduledDate), 'h:mm a')}
                                                </div>
                                            )}
                                        </div>
                                    ))}
                                    {dayPosts.length > 3 && (
                                        <div className="text-xs text-slate-500 text-center py-1 bg-slate-50 rounded">
                                            +{dayPosts.length - 3} more posts
                                        </div>
                                    )}
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>
        </div>
    );
};

export default CalendarView;
