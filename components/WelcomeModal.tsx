import React from 'react';
import Modal from './ui/Modal';
import Button from './ui/Button';
import { RocketIcon } from './icons/RocketIcon';

interface WelcomeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onStartOnboarding: () => void;
}

const WelcomeModal: React.FC<WelcomeModalProps> = ({ isOpen, onClose, onStartOnboarding }) => {
  if (!isOpen) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Welcome to PulseCraft!">
      <div className="p-6 text-center">
        <div className="flex justify-center mb-4">
          <div className="p-3 bg-purple-100 rounded-full">
            <RocketIcon className="w-8 h-8 text-purple-600" />
          </div>
        </div>
        <p className="text-lg text-slate-700 mb-4">
          We're excited to have you on board. Let's get your brand strategy set up.
        </p>
        <p className="text-sm text-slate-500 mb-6">
          Follow a quick guided tour to learn how to make the most out of PulseCraft.
        </p>
        <Button onClick={onStartOnboarding} size="md">
          Start Guided Tour
        </Button>
      </div>
    </Modal>
  );
};

export default WelcomeModal;
