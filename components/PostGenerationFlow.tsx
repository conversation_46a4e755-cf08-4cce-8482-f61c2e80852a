

import React from 'react';
import Button from './ui/Button';
import Loader from './ui/Loader';
import { SparklesIcon } from './icons/SparklesIcon';
import { COSTS } from '../contexts/AppContext';
import { useSettings } from '../contexts/AppContext';

interface PostGenerationFlowProps {
    onGenerate: () => Promise<boolean>;
    onSkip: () => void;
}

const PostGenerationFlow: React.FC<PostGenerationFlowProps> = ({ onGenerate, onSkip }) => {
    const { settings, loadingStates } = useSettings();
    const isLoading = loadingStates.firstMonth;

    const canGenerate = settings.role === 'admin' || settings.credits >= COSTS.FIRST_MONTH_BATCH;
    const disabledTitle = !canGenerate ? `Insufficient credits. Needs ${COSTS.FIRST_MONTH_BATCH}.` : `Generate first month of content for ${COSTS.FIRST_MONTH_BATCH} credits.`;

    return (
        <div className="flex flex-col justify-center items-center h-full min-h-[500px] text-center bg-slate-50 rounded-lg p-6">
            <div className="w-20 h-20 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 relative">
                <SparklesIcon className="w-10 h-10" />
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold border-2 border-slate-50">
                    ✓
                </div>
            </div>
            <h2 className="text-3xl font-bold text-slate-800">Your Strategic Plan is Ready!</h2>
            <p className="mt-2 text-slate-500 max-w-lg mx-auto">
                The high-level strategy is complete. Now, let's bring it to life by generating the content for your first month.
            </p>

            <div className="my-8 p-6 bg-white border border-slate-200 rounded-lg shadow-sm">
                <h3 className="font-bold text-lg text-slate-800">Next Step: Generate First Month's Posts</h3>
                <p className="text-sm text-slate-500 mt-1">This will create posts for the next 4 weeks based on your new plan, scheduling them automatically.</p>
                <Button 
                    onClick={onGenerate} 
                    disabled={isLoading || !canGenerate} 
                    variant="primary" 
                    size="md" 
                    className="mt-6 px-8 py-3"
                    title={disabledTitle}
                >
                    {isLoading ? (
                        <>
                            <Loader size="sm" className="mr-2" />
                            Generating...
                        </>
                    ) : (
                        `Generate Month 1 (${COSTS.FIRST_MONTH_BATCH} credits)`
                    )}
                </Button>
            </div>

            <Button onClick={onSkip} variant="ghost" className="text-sm text-slate-500 hover:text-slate-800">
                I'll do this later, take me to the brief
            </Button>
        </div>
    );
};

export default PostGenerationFlow;
