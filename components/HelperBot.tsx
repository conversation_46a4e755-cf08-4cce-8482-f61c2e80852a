import React from 'react';
import { HelperBotProps } from '../types';
import Button from './ui/Button';
import { PulseCraftLogo } from './icons/VibePilotLogo';

const HelperBot: React.FC<HelperBotProps> = ({ step, onNext, onPrev, onSkip, isFirstStep, isLastStep, targetRect }) => {
    if (!targetRect) return null;

    const botPositionStyles = (): React.CSSProperties => {
        const gap = 16;
        const baseStyle: React.CSSProperties = {
            position: 'fixed',
            zIndex: 9999,
            transition: 'all 0.4s cubic-bezier(0.25, 1, 0.5, 1)',
            transform: 'translate(-50%, 0)', // Default for top/bottom
        };

        switch (step.position) {
            case 'top':
                return { ...baseStyle, top: targetRect.top - gap, left: targetRect.left + targetRect.width / 2, transform: 'translate(-50%, -100%)' };
            case 'left':
                return { ...baseStyle, top: targetRect.top + targetRect.height / 2, left: targetRect.left - gap, transform: 'translate(-100%, -50%)' };
            case 'right':
                return { ...baseStyle, top: targetRect.top + targetRect.height / 2, left: targetRect.right + gap, transform: 'translate(0, -50%)' };
            case 'bottom':
            default:
                return { ...baseStyle, top: targetRect.bottom + gap, left: targetRect.left + targetRect.width / 2 };
        }
    };

    return (
        <div 
            style={botPositionStyles()}
            className="w-80 bg-white rounded-xl shadow-2xl animate-fade-in"
            role="dialog"
            aria-labelledby="helper-bot-title"
        >
            <div className="p-5">
                <div className="flex items-center space-x-3 mb-3">
                    <div className="flex-shrink-0">
                        <PulseCraftLogo className="w-10 h-10"/>
                    </div>
                    <h3 id="helper-bot-title" className="text-lg font-bold text-slate-800">{step.title}</h3>
                </div>
                <p className="text-sm text-slate-600 leading-relaxed">
                    {step.text}
                </p>
            </div>
            <div className="px-5 py-3 bg-slate-50 border-t border-slate-200 flex justify-between items-center rounded-b-xl">
                <Button onClick={onSkip} variant="ghost" size="sm">Skip Tour</Button>
                <div className="flex space-x-2">
                    {!isFirstStep && <Button onClick={onPrev} variant="outline" size="sm">Back</Button>}
                    <Button onClick={onNext} variant="primary" size="sm">
                        {isLastStep ? 'Finish' : 'Next'}
                    </Button>
                </div>
            </div>
            <style>{`
                @keyframes fade-in {
                    from { opacity: 0; transform: translateY(10px) scale(0.98); }
                    to { opacity: 1; transform: translateY(0) scale(1); }
                }
                .animate-fade-in { animation: fade-in 0.3s ease-out; }
            `}</style>
        </div>
    );
};

export default HelperBot;
