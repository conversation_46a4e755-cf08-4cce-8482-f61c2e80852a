import React from 'react';
import { LightbulbIcon } from './icons/LightbulbIcon';
import Button from './ui/Button';
import ComingSoonMarker from './ui/ComingSoonMarker';

const AIInsights: React.FC = () => {
  return (
    <div className="relative bg-white p-4 rounded-lg border border-slate-200 shadow-sm">
      <ComingSoonMarker />
      <div className="flex items-center mb-4">
        <div className="p-2 bg-purple-100 rounded-full mr-3">
          <LightbulbIcon className="w-6 h-6 text-purple-600" />
        </div>
        <h3 className="text-xl font-bold text-slate-800">AI Insights & Recommendations</h3>
      </div>
      <div className="space-y-4">
        <div className="p-3 bg-slate-50 rounded-lg">
          <p className="text-sm text-slate-700">
            Your post about <span className="font-semibold text-purple-600">"<PERSON>"</span> is in your top 10% for engagement. Consider writing another post on a similar topic.
          </p>
          <Button size="xs" variant="secondary" className="mt-2">Draft a post</Button>
        </div>
        <div className="p-3 bg-slate-50 rounded-lg">
          <p className="text-sm text-slate-700">
            Your audience was most active at <span className="font-semibold text-purple-600">7 PM</span> last week. Schedule your next post for this time?
          </p>
          <Button size="xs" variant="secondary" className="mt-2">Schedule now</Button>
        </div>
        <div className="p-3 bg-slate-50 rounded-lg">
          <p className="text-sm text-slate-700">
            Trending topic in your industry: <span className="font-semibold text-purple-600">"Sustainable AI"</span>. Want to draft a post about it?
          </p>
          <Button size="xs" variant="secondary" className="mt-2">Explore topic</Button>
        </div>
      </div>
    </div>
  );
};

export default AIInsights;
