

import React, { useState, useEffect, useRef } from 'react';
import Modal from './ui/Modal';
import { useUI, useApp } from '../contexts/AppContext';
import { IhabBotIcon } from './icons/IhabBotIcon';
import { ChatMessage, View } from '../types';
import { SendIcon } from './icons/SendIcon';
import Loader from './ui/Loader';
import { createIhabChat, askIhab } from '../services/geminiService';
import type { Chat } from '@google/genai';
import Button from './ui/Button';

declare const marked: any;

const getSuggestedQuestions = (view: View): string[] => {
    switch(view) {
        case 'dashboard':
            return ["What's the purpose of the Dashboard?", "How do I generate more content?", "Where can I change my settings?"];
        case 'strategy':
            return ["How do I refine my plan?", "What is a 'strategic framework'?", "How do I generate posts for a specific week?"];
        case 'planner':
            return ["How do I reschedule a post?", "Can I download all my content?", "What's the difference between Grid and Agenda view?"];
        default:
            return ["What is PulseCraft?", "How do I get started?", "What's the 'Strategy Brief'?"];
    }
};

const AskIhabModal: React.FC = () => {
    const { view, isAskIhabModalOpen, setAskIhabModalOpen } = useUI();
    const [chat, setChat] = useState<Chat | null>(null);
    const [history, setHistory] = useState<ChatMessage[]>([]);
    const [input, setInput] = useState('');
    const [isLoading, setIsLoading] = useState(true);
    const messagesEndRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (isAskIhabModalOpen) {
            setIsLoading(true);
            setHistory([]);
            fetch('/documentation.md')
                .then(res => res.text())
                .then(docText => {
                    const newChat = createIhabChat(docText);
                    setChat(newChat);
                    const initialMessage: ChatMessage = {
                        role: 'model',
                        text: `Hi! I'm Ihab. I see you're on the **${view.charAt(0).toUpperCase() + view.slice(1)}** screen. How can I help you? Here are some questions you can ask:`
                    };
                    setHistory([initialMessage]);
                })
                .catch(err => {
                    console.error("Failed to load documentation for Ihab:", err);
                    setHistory([{ role: 'model', text: 'Sorry, I seem to be having trouble accessing my knowledge base right now. Please try again later.'}]);
                })
                .finally(() => setIsLoading(false));
        }
    }, [isAskIhabModalOpen, view]);
    
    useEffect(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [history]);

    const handleSend = async (question?: string) => {
        const messageToSend = question || input;
        if (!messageToSend.trim() || !chat || isLoading) return;

        const userMessage: ChatMessage = { role: 'user', text: messageToSend };
        setHistory(prev => [...prev, userMessage]);
        setInput('');
        setIsLoading(true);

        try {
            const response = await askIhab(chat, messageToSend);
            const modelMessage: ChatMessage = { role: 'model', text: response.text };
            setHistory(prev => [...prev, modelMessage]);
        } catch (error) {
            console.error("Ask Ihab Error:", error);
            const errorMessage: ChatMessage = { role: 'model', text: "I'm sorry, I encountered an error. Please try asking again." };
            setHistory(prev => [...prev, errorMessage]);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Modal 
            isOpen={isAskIhabModalOpen} 
            onClose={() => setAskIhabModalOpen(false)} 
            title="Ask Ihab" 
            size="2xl"
        >
            <div className="flex flex-col h-[70vh]">
                <div className="flex-grow overflow-y-auto pr-4 -mr-4 space-y-4 modal-content">
                    {history.map((msg, index) => (
                        <div key={index} className={`flex items-start gap-3 ${msg.role === 'user' ? 'justify-end' : ''}`}>
                            {msg.role === 'model' && (
                                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center">
                                    <IhabBotIcon className="w-6 h-6 text-purple-600"/>
                                </div>
                            )}
                            <div className={`max-w-md p-3 rounded-lg ${msg.role === 'user' ? 'bg-purple-600 text-white' : 'bg-slate-100 text-slate-800'}`}>
                                <div className="prose prose-sm max-w-none prose-slate" dangerouslySetInnerHTML={{ __html: marked.parse(msg.text) }} />
                            </div>
                        </div>
                    ))}
                    {isLoading && history.length > 0 && (
                        <div className="flex items-start gap-3">
                             <div className="flex-shrink-0 w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center">
                                <IhabBotIcon className="w-6 h-6 text-purple-600"/>
                            </div>
                            <div className="max-w-md p-3 rounded-lg bg-slate-100 text-slate-800">
                                <Loader size="sm" />
                            </div>
                        </div>
                    )}
                    <div ref={messagesEndRef} />
                </div>
                
                <div className="pt-4 mt-auto">
                    <div className="flex flex-wrap gap-2 mb-2">
                        {getSuggestedQuestions(view).map((q, i) => (
                             <button 
                                key={i} 
                                onClick={() => handleSend(q)}
                                className="px-3 py-1 text-xs font-medium text-purple-700 bg-purple-100 rounded-full hover:bg-purple-200 transition-colors"
                                disabled={isLoading}
                             >
                                 {q}
                             </button>
                        ))}
                    </div>
                    <div className="flex items-center space-x-2">
                        <input
                            type="text"
                            value={input}
                            onChange={(e) => setInput(e.target.value)}
                            onKeyDown={(e) => { if (e.key === 'Enter') handleSend(); }}
                            placeholder="Ask a question about this screen..."
                            className="input flex-grow"
                            disabled={isLoading}
                        />
                        <Button onClick={() => handleSend()} disabled={!input.trim() || isLoading}>
                            <SendIcon className="w-5 h-5" />
                        </Button>
                    </div>
                </div>
            </div>
            <style>{`.input { display: block; width: 100%; border-radius: 0.375rem; border: 1px solid #e2e8f0; padding: 0.5rem 0.75rem; background-color: white; line-height: 1.5; color: #0f172a; } .input:focus { outline: 2px solid transparent; outline-offset: 2px; border-color: #9333ea; box-shadow: 0 0 0 2px #e9d5ff; }`}</style>
        </Modal>
    );
};

export default AskIhabModal;