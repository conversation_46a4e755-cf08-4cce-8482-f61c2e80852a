import React from 'react';
import Modal from './ui/Modal';
import Loader from './ui/Loader';
import { ImageIcon } from './icons/ImageIcon';

interface ImageSelectionModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSelect: (imageSrc: string) => void;
    images: string[];
    isGenerating: boolean;
}

const ImageSelectionModal: React.FC<ImageSelectionModalProps> = ({ isOpen, onClose, onSelect, images, isGenerating }) => {
    return (
        <Modal isOpen={isOpen} onClose={onClose} title="Select Your Favorite Image" size="4xl">
            <div className="p-2">
                {isGenerating ? (
                    <div className="flex flex-col items-center justify-center h-96">
                        <Loader size="lg" />
                        <p className="mt-4 text-slate-600 font-medium">Generating image variations...</p>
                    </div>
                ) : (
                    <>
                        <p className="text-sm text-slate-600 mb-4 text-center">Click an image to select it for your post.</p>
                        <div className="grid grid-cols-2 gap-4">
                            {images.map((imgSrc, index) => (
                                <div
                                    key={index}
                                    className="aspect-square bg-slate-100 rounded-lg overflow-hidden cursor-pointer group relative transition-transform duration-200 hover:scale-[1.02]"
                                    onClick={() => onSelect(imgSrc)}
                                >
                                    <img src={imgSrc} alt={`Image variation ${index + 1}`} className="w-full h-full object-cover" />
                                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 flex items-center justify-center transition-opacity">
                                        <div className="text-white font-bold text-lg flex items-center bg-black/50 px-4 py-2 rounded-full">
                                            <ImageIcon className="w-5 h-5 mr-2" />
                                            Select This Image
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </>
                )}
            </div>
        </Modal>
    );
};

export default ImageSelectionModal;
