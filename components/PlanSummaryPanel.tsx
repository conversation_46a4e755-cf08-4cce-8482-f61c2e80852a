import React, { useEffect, useMemo } from 'react';
import { CloseIcon } from './icons/CloseIcon';
import { usePlan, useUI } from '../contexts/AppContext';
import { parsePlanToHierarchy } from '../utils/planParser';
import Accordion from './ui/Accordion';

declare const marked: any;

const PlanSummaryPanel: React.FC = () => {
    const { planMarkdown } = usePlan();
    const { isPlanSummaryOpen, setPlanSummaryOpen } = useUI();
    const isOpen = isPlanSummaryOpen;
    const onClose = () => setPlanSummaryOpen(false);

    const hierarchy = useMemo(() => parsePlanToHierarchy(planMarkdown), [planMarkdown]);

    useEffect(() => {
        const handleEsc = (event: KeyboardEvent) => {
            if (event.key === 'Escape') onClose();
        };
        if (isOpen) {
            document.body.style.overflow = 'hidden';
            window.addEventListener('keydown', handleEsc);
        }
        return () => {
            document.body.style.overflow = 'auto';
            window.removeEventListener('keydown', handleEsc);
        };
    }, [isOpen, onClose]);

    return (
        <>
            <div 
                className={`fixed inset-0 bg-slate-900/60 backdrop-blur-sm z-40 transition-opacity duration-300 ${isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}
                onClick={onClose}
                role="dialog"
                aria-modal="true"
                aria-labelledby="plan-summary-title"
            />
            <aside 
                className={`fixed top-0 right-0 h-full w-full max-w-lg bg-white shadow-2xl z-50 transform transition-transform duration-300 ease-in-out ${isOpen ? 'translate-x-0' : 'translate-x-full'}`}
            >
                <div className="flex flex-col h-full">
                    <header className="p-4 border-b border-slate-200 flex justify-between items-center flex-shrink-0">
                        <h2 id="plan-summary-title" className="text-lg font-bold text-slate-800">Strategic Plan Summary</h2>
                        <button onClick={onClose} className="p-1 rounded-full text-slate-500 hover:text-slate-900 hover:bg-slate-100 transition-colors" aria-label="Close plan summary">
                            <CloseIcon className="w-6 h-6" />
                        </button>
                    </header>
                    <div className="p-6 flex-grow overflow-y-auto modal-content">
                        <div className="space-y-4">
                            {Object.entries(hierarchy).map(([yearName, yearData], yearIndex) => (
                                <Accordion key={yearName} title={yearName} initiallyOpen={yearIndex === 0}>
                                    <div className="space-y-3">
                                        {Object.entries(yearData).map(([quarterName, quarterData]) => (
                                            <Accordion key={quarterName} title={quarterName} initiallyOpen={yearIndex === 0 && quarterName.includes('Q1')}>
                                                <div className="space-y-2">
                                                    {Object.entries(quarterData).map(([monthName, monthData]) => (
                                                        <div key={monthName}>
                                                            <h4 className="text-md font-semibold text-slate-700 mt-2 mb-2 ml-1">{monthName.split(':')[0]}</h4>
                                                            <div className="grid grid-cols-1 gap-3">
                                                                {Object.entries(monthData).map(([weekId, weekData]) => {
                                                                    const weekName = weekId.split(' | ').pop() || weekId;
                                                                    return (
                                                                        <div key={weekId} className="bg-slate-50 p-4 rounded-lg border border-slate-200">
                                                                            <h5 className="font-bold text-slate-800 mb-2">{weekName}</h5>
                                                                            <p className="text-xs text-slate-500"><strong className="text-slate-600">Strategy:</strong> {weekData.strategy}</p>
                                                                        </div>
                                                                    );
                                                                })}
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            </Accordion>
                                        ))}
                                    </div>
                                </Accordion>
                            ))}
                        </div>
                    </div>
                </div>
            </aside>
        </>
    );
};

export default PlanSummaryPanel;
