import React, { useState } from 'react';
import { CONTENT_TEMPLATES, ContentTemplate, getAllIndustries } from '../data/templates';
import Button from './ui/Button';
import { CheckCircleIcon } from './icons/CheckCircleIcon';
import { SparklesIcon } from './icons/SparklesIcon';

interface TemplateSelectorProps {
  onSelectTemplate: (template: ContentTemplate) => void;
  onSkip: () => void;
}

const TemplateSelector: React.FC<TemplateSelectorProps> = ({ onSelectTemplate, onSkip }) => {
  const [selectedIndustry, setSelectedIndustry] = useState<string>('all');
  const [selectedTemplate, setSelectedTemplate] = useState<ContentTemplate | null>(null);

  const industries = getAllIndustries();
  const filteredTemplates = selectedIndustry === 'all' 
    ? CONTENT_TEMPLATES 
    : CONTENT_TEMPLATES.filter(t => t.industry === selectedIndustry);

  const handleTemplateSelect = (template: ContentTemplate) => {
    setSelectedTemplate(template);
  };

  const handleUseTemplate = () => {
    if (selectedTemplate) {
      onSelectTemplate(selectedTemplate);
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="text-center mb-8">
        <div className="flex items-center justify-center mb-4">
          <SparklesIcon className="w-8 h-8 text-purple-500 mr-2" />
          <h2 className="text-3xl font-bold text-slate-800">Quick Start Templates</h2>
        </div>
        <p className="text-lg text-slate-600 max-w-2xl mx-auto">
          Get started instantly with proven content strategies tailored to your industry. 
          Each template includes 6 months of strategic content planning.
        </p>
      </div>

      {/* Industry Filter */}
      <div className="mb-8">
        <div className="flex flex-wrap justify-center gap-2">
          <button
            onClick={() => setSelectedIndustry('all')}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              selectedIndustry === 'all'
                ? 'bg-purple-100 text-purple-800 border-2 border-purple-300'
                : 'bg-slate-100 text-slate-600 hover:bg-slate-200 border-2 border-transparent'
            }`}
          >
            All Industries
          </button>
          {industries.map(industry => (
            <button
              key={industry}
              onClick={() => setSelectedIndustry(industry)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                selectedIndustry === industry
                  ? 'bg-purple-100 text-purple-800 border-2 border-purple-300'
                  : 'bg-slate-100 text-slate-600 hover:bg-slate-200 border-2 border-transparent'
              }`}
            >
              {industry}
            </button>
          ))}
        </div>
      </div>

      {/* Template Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {filteredTemplates.map(template => (
          <div
            key={template.id}
            className={`relative bg-white rounded-xl border-2 p-6 cursor-pointer transition-all hover:shadow-lg ${
              selectedTemplate?.id === template.id
                ? 'border-purple-300 bg-purple-50 shadow-lg'
                : 'border-slate-200 hover:border-slate-300'
            }`}
            onClick={() => handleTemplateSelect(template)}
          >
            {selectedTemplate?.id === template.id && (
              <div className="absolute top-4 right-4 w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
                <CheckCircleIcon className="w-4 h-4 text-white" />
              </div>
            )}
            
            <div className="mb-4">
              <h3 className="text-xl font-bold text-slate-800 mb-2">{template.name}</h3>
              <span className="inline-block px-3 py-1 bg-slate-100 text-slate-600 rounded-full text-sm font-medium">
                {template.industry}
              </span>
            </div>
            
            <p className="text-slate-600 mb-4 text-sm leading-relaxed">
              {template.description}
            </p>
            
            <div className="mb-4">
              <h4 className="font-semibold text-slate-700 mb-2 text-sm">Target Audience:</h4>
              <p className="text-slate-600 text-sm">{template.targetAudience}</p>
            </div>
            
            <div className="mb-4">
              <h4 className="font-semibold text-slate-700 mb-2 text-sm">Platforms:</h4>
              <div className="flex flex-wrap gap-1">
                {template.platforms.map(platform => (
                  <span
                    key={platform}
                    className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium capitalize"
                  >
                    {platform === 'x-twitter' ? 'X' : platform}
                  </span>
                ))}
              </div>
            </div>
            
            <div className="border-t border-slate-200 pt-4">
              <h4 className="font-semibold text-slate-700 mb-2 text-sm">Sample Content:</h4>
              <div className="bg-slate-50 rounded-lg p-3">
                <p className="text-xs text-slate-600 italic">
                  "{template.samplePosts[0]?.caption.slice(0, 100)}..."
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row justify-center gap-4">
        {selectedTemplate && (
          <Button
            onClick={handleUseTemplate}
            variant="primary"
            size="lg"
            className="px-8 py-3"
          >
            Use "{selectedTemplate.name}" Template
          </Button>
        )}
        <Button
          onClick={onSkip}
          variant="outline"
          size="lg"
          className="px-8 py-3"
        >
          Skip Templates - Start from Scratch
        </Button>
      </div>

      {selectedTemplate && (
        <div className="mt-8 bg-green-50 border border-green-200 rounded-lg p-6">
          <h3 className="font-bold text-green-800 mb-3">What you'll get with this template:</h3>
          <ul className="text-green-700 space-y-2 text-sm">
            <li className="flex items-start">
              <CheckCircleIcon className="w-4 h-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
              <span>Complete brand voice and visual style guidelines</span>
            </li>
            <li className="flex items-start">
              <CheckCircleIcon className="w-4 h-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
              <span>6-month strategic content plan with weekly themes</span>
            </li>
            <li className="flex items-start">
              <CheckCircleIcon className="w-4 h-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
              <span>50+ ready-to-customize post templates</span>
            </li>
            <li className="flex items-start">
              <CheckCircleIcon className="w-4 h-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
              <span>Platform-specific content optimizations</span>
            </li>
            <li className="flex items-start">
              <CheckCircleIcon className="w-4 h-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
              <span>Industry-specific hashtag recommendations</span>
            </li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default TemplateSelector;
