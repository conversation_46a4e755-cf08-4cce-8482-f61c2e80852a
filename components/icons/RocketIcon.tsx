
import React from 'react';

interface IconProps extends React.SVGProps<SVGSVGElement> {
    title?: string;
}

export const RocketIcon: React.FC<IconProps> = ({ title, ...props }) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}>
        {title && <title>{title}</title>}
        <path d="M4.5 16.5c-1.5 1.5-3 1.5-4.5 0s0-3 1.5-4.5L13.5 1.5c1.5-1.5 3-1.5 4.5 0s0 3-1.5 4.5L6 16.5" />
        <path d="m7.5 12.5 5 5" />
        <path d="m13.5 6.5 5 5" />
        <path d="m2.5 21.5.5-2.5-2.5.5" />
        <path d="m20 8-.5-2.5-2.5.5" />
    </svg>
);
