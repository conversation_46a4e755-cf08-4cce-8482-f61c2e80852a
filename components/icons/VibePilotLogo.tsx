import React from 'react';

interface LogoProps extends React.SVGProps<SVGSVGElement> {
    isWordmark?: boolean;
    className?: string;
}

export const PulseCraftLogo: React.FC<LogoProps> = ({ isWordmark = false, className, ...props }) => {
    return (
        <svg
            width={isWordmark ? 130 : 40}
            height={40}
            viewBox={isWordmark ? "0 0 130 40" : "0 0 40 40"}
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={className}
            {...props}
        >
            <defs>
                <linearGradient id="logo-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#d946ef" />
                    <stop offset="100%" stopColor="#9333ea" />
                </linearGradient>
            </defs>
            {/* Icon Part */}
            <g>
                <circle cx="20" cy="20" r="16" fill="url(#logo-gradient)" />
                <path d="M11 20H15L17 16L20 24L22 20H26" stroke="white" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
            </g>

            {isWordmark && (
                <text
                    x="48"
                    y="27"
                    fontFamily="'Space Grotesk', sans-serif"
                    fontSize="20"
                    fontWeight="bold"
                    fill="white"
                >
                    PulseCraft
                </text>
            )}
        </svg>
    );
};
