import React from 'react';

interface IconProps extends React.SVGProps<SVGSVGElement> {
    title?: string;
}

export const LightbulbIcon: React.FC<IconProps> = ({ title, ...props }) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}>
        {title && <title>{title}</title>}
        <path d="M15 14c.2-1 .7-1.7 1.5-2.5C17.7 10.2 18 9.2 18 8c0-2.2-1.8-4-4-4-1.9 0-3.5 1.3-3.9 3.1" />
        <path d="M9 18h6" />
        <path d="M10 22h4" />
        <path d="M9 14a3 3 0 1 1 6 0c0 .9-.3 1.6-.8 2.3-.5.7-1.1 1.4-1.2 2.7H11c-.1-1.3-.6-2-1.2-2.7-.5-.7-.8-1.4-.8-2.3z" />
    </svg>
);
