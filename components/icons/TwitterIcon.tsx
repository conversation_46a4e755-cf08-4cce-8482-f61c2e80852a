
import React from 'react';

interface IconProps extends React.SVGProps<SVGSVGElement> {
    title?: string;
}

export const TwitterIcon: React.FC<IconProps> = ({ title, ...props }) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}>
      {title && <title>{title}</title>}
      <path d="M22 4s-.7 2.1-2 3.4c1.6 1.4 3.3 4.9 3 7.1 0 .8-1.5 1.5-3 1.5-1.5 0-2.8-.8-3.5-1.4-1.2 1.2-3.5 2.1-6.5 2.1-3.5 0-6-1.2-7-3.3-1-2-.5-4.6 1-6.8 1.5-2.2 3.5-3.6 6-4.2 2-.5 4.1-.3 6-1.2z" />
    </svg>
);
