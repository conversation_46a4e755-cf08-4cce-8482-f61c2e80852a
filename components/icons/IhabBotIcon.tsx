
import React from 'react';

interface IconProps extends React.SVGProps<SVGSVGElement> {
    title?: string;
}

export const IhabBotIcon: React.FC<IconProps> = ({ title, ...props }) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}>
        {title && <title>Ask Ihab</title>}
        <path d="M14.5 10.5a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z"/>
        <path d="M10.5 10.5a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z"/>
        <path d="M12 2a10 10 0 0 0-10 10 10 10 0 0 0 6 9.34v-2.34a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2.34A10 10 0 0 0 22 12 10 10 0 0 0 12 2Z"/>
        <path d="M9 17a3 3 0 0 1 6 0"/>
    </svg>
);
