import React from 'react';

interface IconProps extends React.SVGProps<SVGSVGElement> {
    title?: string;
}

export const BeakerIcon: React.FC<IconProps> = ({ title, ...props }) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}>
        {title && <title>{title}</title>}
        <path d="M4.5 3h15"/>
        <path d="M6 3v12c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V3"/>
        <path d="M6 14h12"/>
    </svg>
);
