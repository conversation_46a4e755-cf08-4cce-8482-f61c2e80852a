import React from 'react';

interface IconProps extends React.SVGProps<SVGSVGElement> {
    title?: string;
}

export const RecycleIcon: React.FC<IconProps> = ({ title, ...props }) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}>
        {title && <title>{title}</title>}
        <path d="M7 19H4.5a1.5 1.5 0 0 1 0-3H7"/>
        <path d="M11 16h2.5a1.5 1.5 0 0 1 0 3H11"/>
        <path d="M16.5 16H14a1.5 1.5 0 0 0 0 3h2.5"/>
        <path d="m18.14 14.37-1.18.9a2 2 0 0 1-2.82 0l-.8-1.6a2 2 0 0 0-2.82 0l-1.18.9"/>
        <path d="m18.14 9.63-1.18-.9a2 2 0 0 0-2.82 0l-.8 1.6a2 2 0 0 1-2.82 0l-1.18-.9"/>
        <path d="m14.53 5.27 1.18.9a2 2 0 0 0 2.82 0l.8-1.6a2 2 0 0 0-2.82 0l-1.18.9"/>
        <path d="M7 19v-4.14"/>
        <path d="M11 16V8.4"/>
        <path d="M16.5 16v-4.14"/>
    </svg>
);
