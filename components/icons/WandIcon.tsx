
import React from 'react';

interface IconProps extends React.SVGProps<SVGSVGElement> {
    title?: string;
}

export const WandIcon: React.FC<IconProps> = ({ title, ...props }) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}>
        {title && <title>{title}</title>}
        <path d="M15 4V2" />
        <path d="M15 16v-2" />
        <path d="M12.07 14.58A5 5 0 0 0 9 13H4" />
        <path d="M15 8a5 5 0 0 0-2.93-4.58" />
        <path d="M8 15v2" />
        <path d="M9 4V2" />
        <path d="M17.5 10.5 16 9" />
        <path d="M8 7l-1.5-1.5" />
        <path d="M20 14h2" />
        <path d="M2 14h2" />
        <path d="M17.5 13.5 16 15" />
        <path d="M8 17l-1.5 1.5" />
    </svg>
);
