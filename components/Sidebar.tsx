import React from 'react';
import { HomeIcon } from './icons/HomeIcon';
import { LayoutGridIcon } from './icons/LayoutGridIcon';
import { FileTextIcon } from './icons/FileTextIcon';
import { GearIcon } from './icons/GearIcon';
import Loader from './ui/Loader';
import { LogoutIcon } from './icons/LogoutIcon';
import { MessageSquareIcon } from './icons/MessageSquareIcon';
import { UsersIcon } from './icons/UsersIcon';
import { WandIcon } from './icons/WandIcon';
import { VideoIcon } from './icons/VideoIcon';
import { useSettings, useUI, usePosts } from '../contexts/AppContext';
import { useAuth } from '../contexts/AuthContext';
import { useModals } from '../contexts/ModalContext';
import ComingSoonModal from './ui/ComingSoonModal';
import { useState } from 'react';
import { Post, ALL_PLATFORMS } from '../types';

interface NavItemProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    icon: React.ReactNode;
    label: string;
    isActive: boolean;
    onClick: () => void;
    suffix?: React.ReactNode;
    className?: string;
}

const NavItem: React.FC<NavItemProps> = ({ icon, label, isActive, onClick, suffix, className = '', ...props }) => (
    <button
        onClick={onClick}
        className={`w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg transition-colors text-sm font-medium ${
            isActive
                ? 'bg-purple-100 text-purple-700'
                : 'text-slate-600 hover:bg-slate-100 hover:text-slate-900'
        } ${className}`}
        {...props}
    >
        {icon}
        <span className="flex-grow text-left">{label}</span>
        {suffix && <div className="flex-shrink-0">{suffix}</div>}
    </button>
);

const Sidebar: React.FC = () => {
    const { settings, loadingStates } = useSettings();
    const { view, setView, setSelectedPost } = useUI();
    const { setSettingsOpen, setFeedbackModalOpen } = useModals();
    const { logout } = useAuth();
    const [isComingSoonModalOpen, setComingSoonModalOpen] = useState(false);

    const defaultImg = `https://placehold.co/150x150/E2E8F0/4A5568?text=${settings.brandName ? settings.brandName.charAt(0).toUpperCase() : 'C'}`;

    const handleNewPost = () => {
        // Create a new post with default platform (first available)
        const defaultPlatform = ALL_PLATFORMS[0];
        const newPost: Post = {
            id: 0, // Temporary ID for a new post
            platform: defaultPlatform,
            caption: '',
            hashtags: '',
            image_prompt: '',
            likes: 0,
            comments: 0,
            date: new Date().toISOString(),
            status: 'draft',
        };
        setSelectedPost(newPost);
        // Switch to planner view to show the post
        setView('planner');
    };

    return (
        <aside className="w-64 bg-white border-r border-slate-200 flex flex-col p-4 flex-shrink-0">
            <div className="flex items-center space-x-3 mb-8">
                <img src={settings.profileImageUrl || defaultImg} alt="Profile" className="w-10 h-10 rounded-full object-cover bg-slate-200 border-2 border-white shadow"/>
                <div>
                    <h1 className="font-bold text-slate-800">{settings.brandName || "My Brand"}</h1>
                    <p className="text-xs text-slate-500">PulseCraft</p>
                </div>
                <button onClick={() => setComingSoonModalOpen(true)} className="p-1 rounded-full text-slate-500 hover:text-slate-900 hover:bg-slate-100 transition-colors">
                    <UsersIcon className="w-5 h-5" />
                </button>
            </div>

            <nav className="flex-grow space-y-1">
                 <NavItem
                    icon={<HomeIcon className="w-5 h-5" />}
                    label="Dashboard"
                    isActive={view === 'dashboard'}
                    onClick={() => setView('dashboard')}
                    data-tour-id="nav-dashboard"
                />
                <NavItem
                    icon={<FileTextIcon className="w-5 h-5" />}
                    label="Strategy Brief"
                    isActive={view === 'strategy'}
                    onClick={() => setView('strategy')}
                    suffix={loadingStates.plan ? <Loader size="sm" /> : undefined}
                    data-tour-id="nav-strategy"
                />
                <NavItem
                    icon={<LayoutGridIcon className="w-5 h-5" />}
                    label="Content Planner"
                    isActive={view === 'planner'}
                    onClick={() => setView('planner')}
                    data-tour-id="nav-planner"
                />
            </nav>

            <div className="mt-4">
                <div className="px-3 py-2 text-xs font-semibold text-slate-400 uppercase tracking-wider">
                    Quick Actions
                </div>
                <NavItem
                    icon={<WandIcon className="w-5 h-5" />}
                    label="New Post"
                    isActive={false}
                    onClick={handleNewPost}
                />
                <NavItem
                    icon={<VideoIcon className="w-5 h-5" />}
                    label="New Story"
                    isActive={false}
                    onClick={() => {}}
                    disabled
                    suffix={<span className="text-xs text-slate-400">Coming Soon</span>}
                />
            </div>

            <div className="mt-4">
                <div className="px-3 py-2 text-xs font-semibold text-slate-400 uppercase tracking-wider">
                    Account
                </div>
                <div className="px-3 py-2 flex justify-between items-center text-sm font-medium text-slate-600">
                    <span>Credits</span>
                    <span className={`font-bold ${settings.role === 'admin' ? 'text-green-600' : 'text-slate-800'}`}>
                        {settings.role === 'admin' ? 'Unlimited' : settings.credits.toLocaleString()}
                    </span>
                </div>
                <NavItem
                    icon={<MessageSquareIcon className="w-5 h-5" />}
                    label="Feedback"
                    isActive={false}
                    onClick={() => setFeedbackModalOpen(true)}
                    className="animate-pulse-glow"
                    data-tour-id="nav-feedback"
                />
                 <NavItem
                    icon={<GearIcon className="w-5 h-5" />}
                    label="Settings"
                    isActive={false} // Settings is a modal, not a view
                    onClick={() => setSettingsOpen(true)}
                    data-tour-id="nav-settings"
                />
                <NavItem
                    icon={<LogoutIcon className="w-5 h-5" />}
                    label="Logout"
                    isActive={false}
                    onClick={logout}
                />
            </div>
            <ComingSoonModal isOpen={isComingSoonModalOpen} onClose={() => setComingSoonModalOpen(false)} />
        </aside>
    );
};

export default Sidebar;
