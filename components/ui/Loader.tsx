import React from 'react';

interface LoaderProps {
    size?: 'sm' | 'md' | 'lg';
    className?: string;
}

const Loader: React.FC<LoaderProps> = ({ size = 'md', className = '' }) => {
    const baseClasses = 'border-slate-200 border-t-purple-600 rounded-full animate-spin';
    
    const sizeClasses = {
        sm: 'w-5 h-5 border-2',
        md: 'w-10 h-10 border-4',
        lg: 'w-16 h-16 border-4',
    };

    return (
        <div className={`${baseClasses} ${sizeClasses[size]} ${className}`}></div>
    );
};

export default Loader;