import React from 'react';
import Modal from './Modal';
import Button from './Button';

interface ConfirmationModalProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
    title: string;
    children: React.ReactNode;
    confirmText?: string;
    cancelText?: string;
    size?: 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl';
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
    isOpen,
    onClose,
    onConfirm,
    title,
    children,
    confirmText = 'Confirm',
    cancelText = 'Cancel',
    size = '2xl'
}) => {
    return (
        <Modal isOpen={isOpen} onClose={onClose} title={title} size={size}>
            <div className="flex-grow">
                {children}
            </div>
            <div className="flex justify-end space-x-2 mt-6 pt-4 border-t -mx-6 -mb-6 px-6 pb-4 bg-slate-50 rounded-b-xl">
                <Button onClick={onClose} variant="outline">{cancelText}</Button>
                <Button onClick={onConfirm} variant="primary">{confirmText}</Button>
            </div>
        </Modal>
    );
};

export default ConfirmationModal;