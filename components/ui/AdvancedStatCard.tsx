import React from 'react';
import Sparkline from './Sparkline';
import ComingSoonMarker from './ComingSoonMarker';
import { TrendingUpIcon } from '../icons/TrendingUpIcon';
import { TrendingDownIcon } from '../icons/TrendingDownIcon';

interface AdvancedStatCardProps {
  title: string;
  value: string;
  percentageChange: number;
  data: number[];
  icon: React.ReactNode;
}

const AdvancedStatCard: React.FC<AdvancedStatCardProps> = ({ title, value, percentageChange, data, icon }) => {
  const isPositive = percentageChange >= 0;

  return (
    <div className="relative bg-white p-4 rounded-lg border border-slate-200 flex justify-between items-start shadow-sm">
      <ComingSoonMarker />
      <div>
        <div className="p-3 rounded-full bg-purple-100 text-purple-600 inline-block">
          {icon}
        </div>
        <p className="text-sm text-slate-500 font-medium mt-4">{title}</p>
        <div className="flex items-baseline space-x-2 mt-1">
          <p className="text-2xl font-bold text-slate-800">{value}</p>
          <div className={`flex items-center text-sm font-semibold ${isPositive ? 'text-green-500' : 'text-red-500'}`}>
            {isPositive ? <TrendingUpIcon className="w-4 h-4" /> : <TrendingDownIcon className="w-4 h-4" />}
            <span>{Math.abs(percentageChange)}%</span>
          </div>
        </div>
      </div>
      <div className="mt-2">
        <Sparkline data={data} width={120} height={40} />
      </div>
    </div>
  );
};

export default AdvancedStatCard;
