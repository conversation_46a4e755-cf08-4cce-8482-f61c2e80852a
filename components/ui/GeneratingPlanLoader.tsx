import React, { useState, useEffect } from 'react';
import { Settings } from '../../types';
import { LightbulbIcon } from '../icons/LightbulbIcon';
import { QuoteIcon } from '../icons/QuoteIcon';
import { TrendingUpIcon } from '../icons/TrendingUpIcon';

interface GeneratingPlanLoaderProps {
    settings: Settings;
}

const steps = [
    "Analyzing business context...",
    "Defining brand voice...",
    "Selecting strategic framework...",
    "Crafting Q1 strategy...",
    "Developing Q2 content angles...",
    "Outlining Q3 engagement tactics...",
    "Finalizing Q4 performance metrics...",
    "Assembling the 2-year plan...",
];

const tips = [
    { type: 'quote', text: "“Good marketing makes the company look smart. Great marketing makes the customer feel smart.” - <PERSON>", icon: <QuoteIcon className="w-5 h-5" /> },
    { type: 'tip', text: "Tip: After your plan is generated, use the AI Co-pilot in the 'Action Brief' to refine specific weeks or quarters.", icon: <LightbulbIcon className="w-5 h-5" /> },
    { type: 'trend', text: "Trend 2025: Short-form video continues to dominate. Think about how your pillar content can be turned into engaging Reels or Shorts.", icon: <TrendingUpIcon className="w-5 h-5" /> },
    { type: 'quote', text: "“Content is king, but distribution is queen and she wears the pants.” - Jonathan Perelman", icon: <QuoteIcon className="w-5 h-5" /> },
    { type: 'tip', text: "Tip: Don't forget to fill out the 'Visual Style' in settings for more accurate AI-generated image prompts.", icon: <LightbulbIcon className="w-5 h-5" /> },
    { type: 'trend', text: "Trend 2025: Hyper-personalization is key. Use your plan's audience segments to tailor your messaging.", icon: <TrendingUpIcon className="w-5 h-5" /> },
];

const GeneratingPlanLoader: React.FC<GeneratingPlanLoaderProps> = ({ settings }) => {
    const [currentStep, setCurrentStep] = useState(0);
    const [currentTip, setCurrentTip] = useState(0);

    useEffect(() => {
        const stepInterval = setInterval(() => {
            setCurrentStep(prev => (prev < steps.length - 1 ? prev + 1 : prev));
        }, 2500); // Change step every 2.5 seconds

        const tipInterval = setInterval(() => {
            setCurrentTip(prev => (prev + 1) % tips.length);
        }, 7000); // Change tip every 7 seconds

        return () => {
            clearInterval(stepInterval);
            clearInterval(tipInterval);
        };
    }, []);

    return (
        <div className="flex flex-col justify-center items-center h-full min-h-[500px] text-center bg-slate-50 rounded-lg p-6">
            <div className="relative">
                <div className="w-24 h-24 rounded-full border-8 border-slate-200"></div>
                <div className="absolute inset-0 w-24 h-24 rounded-full border-8 border-t-purple-600 border-l-purple-600 animate-spin"></div>
                <div className="absolute inset-0 flex items-center justify-center font-bold text-purple-600 text-lg">
                    AI
                </div>
            </div>
            
            <h2 className="mt-8 text-xl font-bold text-slate-800">Your Co-pilot is thinking...</h2>
            <p className="text-slate-500">Crafting a bespoke strategy for <span className="font-semibold text-slate-600">{settings.brandName}</span>.</p>

            <div className="w-full max-w-md my-8 text-left">
                <ul className="space-y-3">
                    {steps.map((step, index) => (
                        <li key={index} className={`flex items-center transition-all duration-500 ${currentStep >= index ? 'opacity-100' : 'opacity-40'}`}>
                            <div className={`w-5 h-5 rounded-full mr-3 flex items-center justify-center flex-shrink-0 ${currentStep > index ? 'bg-green-500' : 'bg-slate-300 animate-pulse'}`}>
                                {currentStep > index && <span className="text-white text-xs">✓</span>}
                            </div>
                            <span className={`font-medium ${currentStep >= index ? 'text-slate-700' : 'text-slate-500'}`}>{step}</span>
                        </li>
                    ))}
                </ul>
            </div>
            
            <div className="w-full max-w-lg mt-4 p-4 bg-white rounded-lg border border-slate-200 shadow-sm animate-fade-in-slow">
                <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 text-purple-500 pt-0.5">
                        {tips[currentTip].icon}
                    </div>
                    <p className="text-sm text-slate-600 text-left">{tips[currentTip].text}</p>
                </div>
            </div>
            <style>{`
                @keyframes fade-in-slow {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
                .animate-fade-in-slow { animation: fade-in-slow 1s ease-in-out; }
            `}</style>
        </div>
    );
};

export default GeneratingPlanLoader;