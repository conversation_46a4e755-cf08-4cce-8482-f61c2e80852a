import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer, LineChart, Line } from 'recharts';

interface ChartData {
    name: string;
    posts?: number;
    likes?: number;
    comments?: number;
}

interface DashboardChartProps {
    data: ChartData[];
    chartType: 'bar' | 'line';
    dataKey: 'posts' | 'likes' | 'comments';
}

const DashboardChart: React.FC<DashboardChartProps> = ({ data, chartType, dataKey }) => {
    return (
        <ResponsiveContainer width="100%" height={300}>
            {chartType === 'bar' ? (
                <BarChart data={data}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey={dataKey} fill="#8884d8" />
                </BarChart>
            ) : (
                <LineChart data={data}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey={dataKey} stroke="#8884d8" />
                </LineChart>
            )}
        </ResponsiveContainer>
    );
};

export default DashboardChart;
