import React from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    children: React.ReactNode;
    variant?: 'primary' | 'secondary' | 'success' | 'accent' | 'danger' | 'ghost' | 'outline' | 'danger_outline';
    size?: 'xs' | 'sm' | 'md';
}

const Button: React.FC<ButtonProps> = ({ children, className = '', variant = 'primary', size = 'md', ...props }) => {
    const baseClasses = 'font-semibold rounded-md transition-all duration-200 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2';
    
    const variantClasses = {
        primary: 'bg-purple-600 text-white hover:bg-purple-700 focus-visible:ring-purple-500',
        secondary: 'bg-fuchsia-500 text-white hover:bg-fuchsia-600 focus-visible:ring-fuchsia-500',
        success: 'bg-green-600 text-white hover:bg-green-700 focus-visible:ring-green-600',
        accent: 'bg-slate-800 text-white hover:bg-slate-900 focus-visible:ring-slate-800',
        danger: 'bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-600',
        danger_outline: 'bg-transparent text-red-600 border border-red-500 hover:bg-red-50 focus-visible:ring-red-500',
        ghost: 'bg-transparent text-slate-700 hover:bg-slate-100 hover:text-slate-900 focus-visible:ring-slate-500',
        outline: 'bg-transparent text-slate-700 border border-slate-300 hover:bg-slate-50 focus-visible:ring-slate-400'
    };
    
    const sizeClasses = {
        xs: 'py-1 px-2.5 text-xs',
        sm: 'py-1.5 px-3 text-sm',
        md: 'py-2 px-5',
    };

    return (
        <button
            className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}
            {...props}
        >
            {children}
        </button>
    );
};

export default Button;