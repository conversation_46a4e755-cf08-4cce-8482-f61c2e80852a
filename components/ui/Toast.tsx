import React from 'react';
import { ToastMessage } from '../../types';
import { CheckCircleIcon } from '../icons/CheckCircleIcon';
import { XCircleIcon } from '../icons/XCircleIcon';
import { CloseIcon } from '../icons/CloseIcon';
import { InfoIcon } from '../icons/InfoIcon';
import Button from './Button';

interface ToastProps {
    toast: ToastMessage;
    onDismiss: (id: number) => void;
}

const toastConfig = {
    success: {
        icon: <CheckCircleIcon className="w-6 h-6 text-green-500" />,
        barClass: 'bg-green-500',
    },
    error: {
        icon: <XCircleIcon className="w-6 h-6 text-red-500" />,
        barClass: 'bg-red-500',
    },
    info: {
        icon: <InfoIcon className="w-6 h-6 text-blue-500" />,
        barClass: 'bg-blue-500',
    },
};

const Toast: React.FC<ToastProps> = ({ toast, onDismiss }) => {
    const config = toastConfig[toast.type];

    const handleActionClick = () => {
        if(toast.action) {
            toast.action.onClick();
            onDismiss(toast.id);
        }
    }

    return (
        <div 
            className="bg-white rounded-lg shadow-2xl flex w-full max-w-sm overflow-hidden animate-slide-in-right"
            role="alert"
            aria-live="assertive"
        >
            <div className={`w-1.5 ${config.barClass}`} />
            <div className="p-4 flex items-start flex-grow">
                <div className="flex-shrink-0 pt-0.5">
                    {config.icon}
                </div>
                <div className="ml-3 flex-1">
                    <p className="text-sm font-medium text-slate-800">
                        {toast.message}
                    </p>
                    {toast.action && (
                         <Button
                            onClick={handleActionClick}
                            variant="ghost"
                            size="sm"
                            className="!p-0 !h-auto mt-1 font-semibold text-purple-600 hover:text-purple-500"
                        >
                            {toast.action.label}
                        </Button>
                    )}
                </div>
            </div>
            <button
                onClick={() => onDismiss(toast.id)}
                className="p-2 text-slate-400 hover:text-slate-600 self-start"
                aria-label="Dismiss notification"
            >
                <CloseIcon className="w-5 h-5" />
            </button>
            <style>{`
                @keyframes slide-in-right {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
                .animate-slide-in-right {
                    animation: slide-in-right 0.3s ease-out forwards;
                }
            `}</style>
        </div>
    );
};

export default Toast;