import React from 'react';
import Modal from './Modal';
import Button from './Button';

interface ComingSoonModalProps {
    isOpen: boolean;
    onClose: () => void;
}

const ComingSoonModal: React.FC<ComingSoonModalProps> = ({ isOpen, onClose }) => {
    return (
        <Modal isOpen={isOpen} onClose={onClose} title="Coming Soon!" size="md">
            <div className="text-center">
                <p className="text-lg text-slate-600 mb-6">
                    The ability to manage multiple business accounts is coming soon!
                </p>
                <Button onClick={onClose} variant="primary">Got it!</Button>
            </div>
        </Modal>
    );
};

export default ComingSoonModal;
