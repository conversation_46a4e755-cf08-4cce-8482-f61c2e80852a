import React, { useState } from 'react';
import { ChevronDownIcon } from '../icons/ChevronDownIcon';

interface AccordionProps {
    title: string;
    children: React.ReactNode;
    initiallyOpen?: boolean;
    badge?: React.ReactNode;
    actionComponent?: React.ReactNode;
}

const Accordion: React.FC<AccordionProps> = ({ title, children, initiallyOpen = false, badge, actionComponent }) => {
    const [isOpen, setIsOpen] = useState(initiallyOpen);

    const handleActionClick = (e: React.MouseEvent) => {
        e.stopPropagation(); // Prevent accordion from toggling
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            setIsOpen(!isOpen);
        }
    };

    return (
        <div className="bg-white border border-slate-200 rounded-lg shadow-sm">
            <div
                onClick={() => setIsOpen(!isOpen)}
                onKeyDown={handleKeyDown}
                className="w-full font-semibold text-slate-800 p-4 text-left flex justify-between items-center transition-colors hover:bg-slate-50 rounded-t-lg cursor-pointer"
                aria-expanded={isOpen}
                role="button"
                tabIndex={0}
            >
                <div className="flex items-center flex-grow min-w-0">
                    <span className="truncate">{title}</span>
                    {badge && <div className="ml-3 flex-shrink-0">{badge}</div>}
                </div>
                <div className="flex items-center flex-shrink-0 pl-4">
                    {actionComponent && <div onClick={handleActionClick}>{actionComponent}</div>}
                    <ChevronDownIcon className={`w-5 h-5 text-slate-500 transition-transform ml-4 ${isOpen ? 'rotate-180' : ''}`} title={isOpen ? 'Collapse' : 'Expand'} />
                </div>
            </div>
            {isOpen && <div className="p-4 border-t border-slate-200 bg-slate-50">{children}</div>}
        </div>
    );
};

export default Accordion;
