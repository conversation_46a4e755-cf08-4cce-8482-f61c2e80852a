import React from 'react';
import RGL, { WidthProvider } from 'react-grid-layout';

const ReactGridLayout = WidthProvider(RGL);

interface FenceProps {
    layout: any[];
    onLayoutChange: (layout: any[]) => void;
    children: React.ReactNode;
}

const Fence: React.FC<FenceProps> = ({ layout, onLayoutChange, children }) => {
    return (
        <ReactGridLayout
            className="layout"
            layout={layout}
            onLayoutChange={onLayoutChange}
            cols={12}
            rowHeight={30}
        >
            {children}
        </ReactGridLayout>
    );
};

export default Fence;
