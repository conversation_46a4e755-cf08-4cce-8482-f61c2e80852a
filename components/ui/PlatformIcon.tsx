
import React from 'react';
import { Platform } from '../../types';
import { InstagramIcon } from '../icons/InstagramIcon';
import { TwitterIcon } from '../icons/TwitterIcon';
import { LinkedInIcon } from '../icons/LinkedInIcon';
import { FacebookIcon } from '../icons/FacebookIcon';

interface PlatformIconProps {
    platform: Platform | string;
    className?: string;
}

const PlatformIcon: React.FC<PlatformIconProps> = ({ platform, className }) => {
    switch (platform) {
        case 'instagram':
            return <InstagramIcon className={className} title="Instagram" />;
        case 'x-twitter':
            return <TwitterIcon className={className} title="X (Twitter)" />;
        case 'linkedin':
            return <LinkedInIcon className={className} title="LinkedIn" />;
        case 'facebook':
            return <FacebookIcon className={className} title="Facebook" />;
        default:
            return null;
    }
};

export default PlatformIcon;
