import React, { useEffect } from 'react';
import { CloseIcon } from '../icons/CloseIcon';

interface ModalProps {
    isOpen: boolean;
    onClose: () => void;
    children: React.ReactNode;
    title?: string;
    size?: 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl';
}

const sizeClasses = {
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    '3xl': 'max-w-3xl',
    '4xl': 'max-w-4xl',
};

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, children, title, size = 'lg' }) => {
    useEffect(() => {
        const handleEsc = (event: KeyboardEvent) => {
            if (event.key === 'Escape') {
                onClose();
            }
        };
        if (isOpen) {
            document.body.style.overflow = 'hidden';
            window.addEventListener('keydown', handleEsc);
        }

        return () => {
            document.body.style.overflow = 'auto';
            window.removeEventListener('keydown', handleEsc);
        };
    }, [isOpen, onClose]);

    if (!isOpen) {
        return null;
    }
    
    const hasHeader = !!title;

    return (
        <div
            className="fixed inset-0 bg-slate-900/60 backdrop-blur-sm flex items-center justify-center p-2 sm:p-4 z-50 animate-fade-in"
            onClick={onClose}
            role="dialog"
            aria-modal="true"
            aria-labelledby={title ? 'modal-title' : undefined}
        >
            <div
                className={`bg-white rounded-xl w-full ${sizeClasses[size]} max-h-[98vh] sm:max-h-[90vh] flex flex-col overflow-hidden shadow-2xl animate-scale-up`}
                onClick={(e) => e.stopPropagation()}
            >
                {hasHeader ? (
                    <>
                        <div className="flex justify-between items-center p-6 border-b border-slate-200 flex-shrink-0">
                            <h2 id="modal-title" className="text-xl font-bold text-slate-800">{title}</h2>
                            <button onClick={onClose} className="p-1 rounded-full text-slate-500 hover:text-slate-900 hover:bg-slate-100 transition-colors" aria-label="Close modal">
                                <CloseIcon className="w-6 h-6" title="Close modal" />
                            </button>
                        </div>
                        <div className="p-6 flex-grow overflow-y-auto flex flex-col">
                            {children}
                        </div>
                    </>
                ) : (
                    children
                )}
            </div>
             <style>{`
                @keyframes fade-in {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
                @keyframes scale-up {
                    from { opacity: 0.8; transform: scale(0.95); }
                    to { opacity: 1; transform: scale(1); }
                }
                .animate-fade-in { animation: fade-in 0.2s ease-out; }
                .animate-scale-up { animation: scale-up 0.2s ease-out; }
            `}</style>
        </div>
    );
};

export default Modal;