
import React, { useState } from 'react';
import Modal from './ui/Modal';
import Button from './ui/Button';
import { SparklesIcon } from './icons/SparklesIcon';
import { PulseCraftLogo } from './icons/VibePilotLogo';
import { useApp, useUI } from '../contexts/AppContext';
import { GiftIcon } from './icons/GiftIcon';

const EarlyAdopterModal: React.FC = () => {
    const { handleCompleteOnboarding, handleStartTour, handleSkipOnboarding } = useApp();
    const { setEarlyAdopterModalOpen } = useUI();
    
    const [step, setStep] = useState(1);
    const [brandName, setBrandName] = useState('');
    const [businessContext, setBusinessContext] = useState('');

    const handleNext = () => setStep(s => s + 1);
    const handleBack = () => setStep(s => s - 1);

    const handleSubmitAndExplore = () => {
        handleCompleteOnboarding({ brandName, businessContext });
    };

    const handleSubmitAndTour = () => {
        handleCompleteOnboarding({ brandName, businessContext });
        handleStartTour();
    };

    const handleSkip = () => {
        handleSkipOnboarding();
    };

    const renderStepContent = () => {
        switch (step) {
            case 1:
                return (
                    <div className="text-center">
                        <PulseCraftLogo className="mx-auto mb-4" />
                        <h2 className="text-3xl font-bold text-slate-800">Welcome, Early Adopter!</h2>
                        <p className="mt-2 text-slate-600 max-w-md mx-auto">
                            Thank you for being one of the first to try PulseCraft. Let's get your first co-pilot ready in 60 seconds.
                        </p>
                    </div>
                );
            case 2:
                return (
                    <div className="text-center">
                        <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-purple-100 mb-5">
                            <GiftIcon className="h-8 w-8 text-purple-600" />
                        </div>
                        <h2 className="text-2xl font-bold text-slate-800">Your Pioneer Perks</h2>
                        <div className="text-left text-sm text-slate-600 bg-slate-50 p-4 mt-4 rounded-lg border border-slate-200 space-y-3">
                           <p>To thank you, we've started you with <strong>500 credits</strong>.</p>
                           <p>If you need more, use promo code <code className="font-bold bg-slate-200 text-purple-700 py-0.5 px-1 rounded">PULSECRAFTPIONEER</code> in Settings for a <strong>5x refill (2,500 credits)</strong>, on us!</p>
                           <p>This lets you experiment freely. We're excited to see what you create!</p>
                        </div>
                    </div>
                );
            case 3:
                return (
                    <div>
                        <h3 className="text-2xl font-bold text-slate-800 text-center">What is your brand's name?</h3>
                        <p className="text-center text-slate-500 mt-1 mb-6">This helps the AI personalize its voice.</p>
                        <input type="text" value={brandName} onChange={(e) => setBrandName(e.target.value)} className="input text-lg text-center" placeholder="e.g., Aura Collective" autoFocus />
                    </div>
                );
            case 4:
                return (
                    <div>
                        <h3 className="text-2xl font-bold text-slate-800 text-center">Describe your business.</h3>
                        <p className="text-center text-slate-500 mt-1 mb-6">What do you do? Who is your audience? What's your vibe?</p>
                        <textarea value={businessContext} onChange={(e) => setBusinessContext(e.target.value)} className="input h-40 text-sm" placeholder="e.g., We're a creative lifestyle brand..." autoFocus />
                    </div>
                );
            case 5:
                 return (
                    <div className="text-center">
                        <div className="w-16 h-16 bg-green-100 text-green-600 rounded-full flex items-center justify-center mx-auto mb-6"><SparklesIcon className="w-8 h-8"/></div>
                        <h2 className="text-3xl font-bold text-slate-800">You're all set!</h2>
                        <p className="mt-2 text-slate-600 max-w-md mx-auto">Your co-pilot is ready. Our guided tour will show you the key features.</p>
                    </div>
                );
            default: return null;
        }
    };

    return (
        <Modal isOpen={true} onClose={() => {}} size="2xl">
            <div className="p-8 flex flex-col items-center justify-center min-h-[400px]">
                {renderStepContent()}
                <div className="mt-8 flex items-center justify-center w-full space-x-3">
                    {step === 1 && <Button onClick={handleSkip} variant="ghost">Skip & Explore</Button>}
                    {step > 2 && step < 5 && <Button onClick={handleBack} variant="outline">Back</Button>}
                    
                    {step === 1 && <Button onClick={handleNext} variant="primary">Let's Go!</Button>}
                    {step === 2 && <Button onClick={handleNext} variant="primary">Awesome, Next</Button>}
                    {step === 3 && <Button onClick={handleNext} variant="primary" disabled={!brandName}>Next</Button>}
                    {step === 4 && <Button onClick={handleNext} variant="primary" disabled={!businessContext}>Finish Setup</Button>}
                    
                    {step === 5 && (
                        <>
                            <Button onClick={handleSubmitAndExplore} variant="outline" className="px-6">Explore On My Own</Button>
                            <Button onClick={handleSubmitAndTour} variant="primary" className="px-6">Start Guided Tour</Button>
                        </>
                    )}
                </div>
                <div className="absolute bottom-4 left-0 right-0 flex justify-center space-x-2">
                    {[1, 2, 3, 4, 5].map(i => <div key={i} className={`w-2 h-2 rounded-full transition-colors ${step >= i ? 'bg-purple-600' : 'bg-slate-300'}`}></div>)}
                </div>
            </div>
             <style>{`.input { display: block; width: 100%; border-radius: 0.375rem; border: 1px solid #e2e8f0; padding: 0.5rem 0.75rem; background-color: white; line-height: 1.5; color: #0f172a; } .input:focus { outline: 2px solid transparent; outline-offset: 2px; border-color: #9333ea; box-shadow: 0 0 0 2px #e9d5ff; }`}</style>
        </Modal>
    );
};

export default EarlyAdopterModal;
