import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Post, Platform, ALL_PLATFORMS, GeneratedPostContent } from '../types';
import { format, parseISO } from 'date-fns';
import { toZonedTime, fromZonedTime } from 'date-fns-tz';
import Modal from './ui/Modal';
import Button from './ui/Button';
import Loader from './ui/Loader';
import ConfirmationModal from './ui/ConfirmationModal';
import { CloseIcon } from './icons/CloseIcon';
import { HeartIcon } from './icons/HeartIcon';
import { CommentIcon } from './icons/CommentIcon';
import { WandIcon } from './icons/WandIcon';
import { CopyIcon } from './icons/CopyIcon';
import { DownloadIcon } from './icons/DownloadIcon';
import { TrashIcon } from './icons/TrashIcon';
import { UploadIcon } from './icons/UploadIcon';
import PlatformIcon from './ui/PlatformIcon';
import { ChevronDownIcon } from './icons/ChevronDownIcon';
import { COSTS } from '../contexts/AppContext';
import { supabase } from '../services/supabaseClient';
import { useSettings, usePosts, useUI, useIntegrations } from '../contexts/AppContext';

const PostModal: React.FC = () => {
    const { settings, loadingStates } = useSettings();
    const { integrations } = useIntegrations();
    const { handleUpdatePost, handleDeletePost, handleGenerateImageForPost, handleImprovePost, handleAdaptPost, imageGenAttemptedForPost, setImageGenAttemptedForPost, handleAddNewPost } = usePosts();
    const { selectedPost, setSelectedPost, addToast } = useUI();

    const post = selectedPost!;

    const [editablePost, setEditablePost] = useState(post);
    const [authorType, setAuthorType] = useState('person');
    const [isDirty, setIsDirty] = useState(false);
    const [isPosting, setIsPosting] = useState(false);
    const [isDeleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
    const [improvementRequest, setImprovementRequest] = useState('');
    const [aiSuggestion, setAiSuggestion] = useState<GeneratedPostContent | null>(null);
    const [copySuccess, setCopySuccess] = useState(false);
    const [isAdaptMenuOpen, setAdaptMenuOpen] = useState(false);
    const [styleOverride, setStyleOverride] = useState('');
    const [suggestionSelection, setSuggestionSelection] = useState({
        caption: true,
        hashtags: true,
        image_prompt: true
    });
    const uploadInputRef = useRef<HTMLInputElement>(null);

    const onClose = () => setSelectedPost(null);

    useEffect(() => {
        setEditablePost(post);
        setIsDirty(false);
        setAiSuggestion(null);
        setImprovementRequest('');
        setStyleOverride('');
    }, [post]);

    // Initialize the default LinkedIn posting identity from Settings when opening the modal
    useEffect(() => {
        if (post.platform === 'linkedin') {
            setAuthorType(settings.defaultLinkedInAuthorType || 'person');
        } else {
            setAuthorType('person');
        }
    }, [post.platform, settings.defaultLinkedInAuthorType]);

    const handleGenerateImage = useCallback(async (promptToUse: string, count: number, override?: string) => {
        setImageGenAttemptedForPost(prev => new Set(prev).add(post.id));
        await handleGenerateImageForPost(post, promptToUse, count, override);
    }, [post, handleGenerateImageForPost, setImageGenAttemptedForPost]);

    const handleImprovement = async () => {
        if (!improvementRequest.trim()) return;
        setAiSuggestion(null);
        const result = await handleImprovePost(editablePost, improvementRequest);
        if (result) setAiSuggestion(result);
    };

    const handleAcceptSuggestion = async () => {
        if (!aiSuggestion) return;
        const updatedPostData = { ...editablePost };
        if (suggestionSelection.caption) updatedPostData.caption = aiSuggestion.caption;
        if (suggestionSelection.hashtags) updatedPostData.hashtags = aiSuggestion.hashtags;
        const imageNeedsUpdate = suggestionSelection.image_prompt && aiSuggestion.image_prompt && aiSuggestion.image_prompt.toLowerCase() !== editablePost.image_prompt.toLowerCase();
        if (imageNeedsUpdate) {
            updatedPostData.image_prompt = aiSuggestion.image_prompt;
            handleUpdatePost(updatedPostData);
            await handleGenerateImageForPost(updatedPostData, aiSuggestion.image_prompt, 1, styleOverride);
        } else {
            handleUpdatePost(updatedPostData);
        }
        addToast("Changes applied successfully!", 'success');
        setAiSuggestion(null);
        setImprovementRequest('');
        setSuggestionSelection({ caption: true, hashtags: true, image_prompt: true });
    };

    const handleAdaptForPlatform = async (targetPlatform: Platform) => {
        if (targetPlatform === editablePost.platform) return;
        setAdaptMenuOpen(false);
        await handleAdaptPost(editablePost, targetPlatform);
    };

    const handleDelete = () => {
        handleDeletePost(post.id);
        setDeleteConfirmOpen(false);
        onClose();
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => {
        const { name, value } = e.target;
        setEditablePost(prev => ({ ...prev, [name]: value }));
        setIsDirty(true);
    };

    const handleScheduledDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { value } = e.target;
        if (value) {
            const userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            const utcDate = fromZonedTime(value, userTimeZone);
            setEditablePost(prev => ({ ...prev, scheduledDate: utcDate.toISOString() }));
        } else {
            setEditablePost(prev => ({ ...prev, scheduledDate: undefined }));
        }
        setIsDirty(true);
    };

    const handleSaveChanges = () => {
        const postToSave = { ...editablePost };
        if (postToSave.platform === 'linkedin') {
            postToSave.authorType = authorType as 'person' | 'organization';
        }

        if (postToSave.id === 0) { // This is a new post
            const { id, ...newPostData } = postToSave;
            handleAddNewPost(newPostData);
        } else { // This is an existing post
            handleUpdatePost(postToSave);
        }
        
        addToast("Changes saved successfully!", 'success');
        setIsDirty(false);
        onClose(); // Close modal on save
    };

    const handlePostNow = async () => {
        setIsPosting(true);
        try {
            await handleSaveChanges(); // Save latest changes first
            const { data, error } = await supabase.functions.invoke('post-now', {
                body: { postId: post.id },
            });
            if (error) throw error;
            addToast(`Successfully posted to ${post.platform}!`, 'success');
        } catch (err: any) {
            addToast(`Failed to post: ${err.message}`, 'error');
        } finally {
            setIsPosting(false);
        }
    };

    const handleCopyCaption = () => {
        navigator.clipboard.writeText(`${editablePost.caption}\n\n${editablePost.hashtags}`);
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000);
    };

    const handleDownloadImage = () => {
        if (!editablePost.imgSrc) return;
        const a = document.createElement('a');
        a.href = editablePost.imgSrc;
        a.download = `${settings.brandName}-${editablePost.platform}-post-${editablePost.id}.jpeg`;
        a.click();
    };

    const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            const dataUrl = e.target?.result as string;
            setEditablePost(prev => ({ ...prev, imgSrc: dataUrl }));
            setIsDirty(true);
        };
        reader.readAsDataURL(file);
    };

    const getLocalDateTimeValue = (isoString?: string) => {
        if (!isoString) return '';
        const userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        const zonedTime = toZonedTime(parseISO(isoString), userTimeZone);
        return format(zonedTime, "yyyy-MM-dd'T'HH:mm");
    };

    const isGeneratingImage = loadingStates.image === post.id;
    const showLoader = loadingStates.improve || loadingStates.adapt || isGeneratingImage;
    const canImprove = settings.role === 'admin' || settings.credits >= COSTS.POST_IMPROVEMENT;
    const canAdapt = settings.role === 'admin' || settings.credits >= COSTS.POST_ADAPTATION;
    const canGenerateVariations = settings.role === 'admin' || settings.credits >= COSTS.IMAGE_VARIATION_GENERATION;

    return (
        <Modal isOpen={true} onClose={onClose} size="4xl">
            <div className="bg-white rounded-lg w-full max-w-4xl max-h-[95vh] lg:max-h-[90vh] flex flex-col lg:flex-row overflow-hidden">
                {/* Image Section - Full width on mobile, half on desktop */}
                <div className="w-full lg:w-1/2 bg-slate-800 flex items-center justify-center relative min-h-[250px] lg:min-h-[400px]">
                    {showLoader && (
                        <div className="absolute inset-0 bg-black bg-opacity-70 flex flex-col items-center justify-center z-10">
                            <Loader />
                            <p className="text-white mt-4 font-medium text-sm">{loadingStates.improve ? "Improving..." : loadingStates.adapt ? "Adapting..." : "Generating..."}</p>
                        </div>
                    )}
                    {editablePost.imgSrc ? (
                        <img
                            src={editablePost.imgSrc}
                            alt="Full post view"
                            className="object-contain max-h-[250px] lg:max-h-[90vh] w-full"
                        />
                    ) : (
                        !isGeneratingImage && (
                            <div className="text-white p-4 text-center">
                                <p className="font-semibold mb-4">Image not available</p>
                                <div className="flex flex-col sm:flex-row gap-2 justify-center">
                                    <Button
                                        onClick={() => handleGenerateImage(editablePost.image_prompt, 1)}
                                        variant="secondary"
                                        size="sm"
                                        disabled={settings.credits < COSTS.IMAGE_GENERATION_PER_IMAGE}
                                        className="min-h-[44px]"
                                    >
                                        Generate Image
                                    </Button>
                                    <Button
                                        onClick={() => uploadInputRef.current?.click()}
                                        variant="outline"
                                        size="sm"
                                        className="min-h-[44px]"
                                    >
                                        Upload Image
                                    </Button>
                                </div>
                            </div>
                        )
                    )}
                </div>
                {/* Content Section */}
                <div className="w-full lg:w-1/2 flex flex-col">
                    <div className="p-4 lg:p-6 border-b border-slate-200 flex-shrink-0">
                        <div className="flex justify-between items-start">
                            <div className="flex-1 min-w-0">
                                <div className="flex items-center space-x-2">
                                    <PlatformIcon platform={editablePost.platform} className="w-5 h-5 lg:w-6 lg:h-6 text-slate-700 flex-shrink-0" />
                                    <h2 className="text-lg lg:text-xl font-bold text-slate-800 capitalize truncate">{editablePost.platform} Post</h2>
                                </div>
                                <div className="mt-2">
                                    <input
                                        type="datetime-local"
                                        id="scheduledDate"
                                        name="scheduledDate"
                                        value={getLocalDateTimeValue(editablePost.scheduledDate)}
                                        onChange={handleScheduledDateChange}
                                        className="text-xs lg:text-sm text-slate-500 bg-transparent rounded-md p-2 -ml-2 hover:bg-slate-100 focus:bg-slate-100 focus:ring-1 focus:ring-purple-500 min-h-[44px] w-full max-w-[200px]"
                                    />
                                </div>
                            </div>
                            <button
                                onClick={onClose}
                                className="p-2 lg:p-1 rounded-full text-slate-500 hover:text-slate-900 hover:bg-slate-100 transition-colors ml-2 min-h-[44px] min-w-[44px] flex items-center justify-center"
                            >
                                <CloseIcon className="w-5 h-5 lg:w-6 lg:h-6" />
                            </button>
                        </div>
                        <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-2 sm:space-y-0 text-sm text-slate-600 mt-3">
                            <div className="flex items-center">
                                <HeartIcon className="w-4 h-4 mr-1.5 text-red-500" />
                                <span>{editablePost.likes.toLocaleString()} Likes</span>
                            </div>
                            <div className="flex items-center">
                                <CommentIcon className="w-4 h-4 mr-1.5 text-sky-500" />
                                <span>{editablePost.comments.toLocaleString()} Comments</span>
                            </div>
                        </div>
                    </div>
                    <div className="flex-grow p-4 lg:p-6 overflow-y-auto modal-content space-y-4 lg:space-y-6">
                        <div>
                            <label className="label">Caption</label>
                            <textarea
                                name="caption"
                                value={editablePost.caption}
                                onChange={handleInputChange}
                                className="input h-32 text-base"
                                rows={4}
                            />
                        </div>
                        <div>
                            <label className="label">Hashtags</label>
                            <input
                                type="text"
                                name="hashtags"
                                value={editablePost.hashtags}
                                onChange={handleInputChange}
                                className="input text-base min-h-[44px]"
                            />
                        </div>
                        <div>
                            <label className="label">Image Prompt (for AI)</label>
                            <textarea
                                name="image_prompt"
                                value={editablePost.image_prompt}
                                onChange={handleInputChange}
                                className="input h-24 text-base"
                                rows={3}
                            />
                        </div>
                        <div>
                            <label htmlFor="styleOverride" className="label">Visual Style Override (Optional)</label>
                            <input
                                id="styleOverride"
                                value={styleOverride}
                                onChange={(e) => setStyleOverride(e.target.value)}
                                className="input text-base min-h-[44px]"
                                placeholder={`e.g., vibrant, pop art style`}
                            />
                            <p className="help-text">Override the global visual style for this image only.</p>
                            <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleGenerateImage(editablePost.image_prompt, 4, styleOverride)}
                                className="mt-2 min-h-[44px] w-full sm:w-auto"
                                disabled={isGeneratingImage || !canGenerateVariations}
                            >
                                {isGeneratingImage ? (
                                    <>
                                        <Loader size="sm" className="mr-1" />
                                        <span className="hidden sm:inline">Generating...</span>
                                        <span className="sm:hidden">Generating</span>
                                    </>
                                ) : (
                                    <>
                                        <span className="hidden sm:inline">Generate 4 Variations ({COSTS.IMAGE_VARIATION_GENERATION} cr)</span>
                                        <span className="sm:hidden">Generate Variations</span>
                                    </>
                                )}
                            </Button>
                        </div>
                        <div className="bg-slate-50 p-3 lg:p-4 rounded-lg border border-slate-200">
                            <h3 className="font-bold text-slate-800 mb-2 flex items-center">
                                <WandIcon className="w-5 h-5 mr-2 text-purple-500" />
                                AI Co-pilot
                            </h3>
                            <textarea
                                value={improvementRequest}
                                onChange={(e) => setImprovementRequest(e.target.value)}
                                className="input h-20 text-base"
                                placeholder="e.g., Make it funnier..."
                                disabled={loadingStates.improve || !!aiSuggestion}
                                rows={3}
                            />
                            <Button
                                onClick={handleImprovement}
                                disabled={!improvementRequest.trim() || loadingStates.improve || !!aiSuggestion || !canImprove}
                                className="mt-2 min-h-[44px] w-full sm:w-auto"
                            >
                                <span className="hidden sm:inline">Suggest Improvement ({COSTS.POST_IMPROVEMENT} credits)</span>
                                <span className="sm:hidden">Improve ({COSTS.POST_IMPROVEMENT} cr)</span>
                            </Button>
                        </div>
                        {aiSuggestion && (
                            <div className="p-4 rounded-lg bg-purple-50 border border-purple-200 animate-fade-in">
                                <h4 className="font-bold text-purple-800">✨ AI Suggestion</h4>
                                <div className="mt-2 space-y-1 text-sm text-slate-700 bg-white p-3 rounded-md">
                                    <label className="flex items-start space-x-3 p-2 rounded hover:bg-slate-50 cursor-pointer"><input type="checkbox" className="mt-1 shrink-0" checked={suggestionSelection.caption} onChange={e => setSuggestionSelection(s => ({ ...s, caption: e.target.checked }))} /><div><strong>Caption:</strong> {aiSuggestion.caption}</div></label>
                                    <label className="flex items-start space-x-3 p-2 rounded hover:bg-slate-50 cursor-pointer"><input type="checkbox" className="mt-1 shrink-0" checked={suggestionSelection.hashtags} onChange={e => setSuggestionSelection(s => ({ ...s, hashtags: e.target.checked }))} /><div><strong>Hashtags:</strong> {aiSuggestion.hashtags}</div></label>
                                    <label className="flex items-start space-x-3 p-2 rounded hover:bg-slate-50 cursor-pointer"><input type="checkbox" className="mt-1 shrink-0" checked={suggestionSelection.image_prompt} onChange={e => setSuggestionSelection(s => ({ ...s, image_prompt: e.target.checked }))} /><div><strong>Image Prompt:</strong> {aiSuggestion.image_prompt}</div></label>
                                </div>
                                <div className="mt-3 flex space-x-2"><Button onClick={handleAcceptSuggestion} variant="success" size="sm" disabled={loadingStates.image !== null}>Apply Changes</Button><Button onClick={() => setAiSuggestion(null)} variant="ghost" size="sm">Discard</Button></div>
                            </div>
                        )}
                    </div>
                    <div className="p-3 lg:p-4 border-t border-slate-200 shrink-0 bg-slate-50/50">
                        {/* Mobile Layout - Stack vertically */}
                        <div className="lg:hidden space-y-3">
                            {/* Primary Actions Row */}
                            <div className="flex justify-between items-center">
                                {isDirty && (
                                    <Button onClick={handleSaveChanges} className="min-h-[44px] flex-1 mr-2">
                                        Save Changes
                                    </Button>
                                )}
                                <Button
                                    onClick={handlePostNow}
                                    variant="primary"
                                    disabled={isPosting}
                                    className="min-h-[44px] flex-1"
                                >
                                    {isPosting ? 'Posting...' : 'Post Now'}
                                </Button>
                            </div>

                            {/* Secondary Actions Row */}
                            <div className="flex justify-between items-center">
                                <div className="flex items-center space-x-2">
                                    <div className="relative">
                                        <Button
                                            onClick={() => setAdaptMenuOpen(o => !o)}
                                            variant="outline"
                                            size="sm"
                                            disabled={loadingStates.adapt || !canAdapt}
                                            className="min-h-[44px]"
                                        >
                                            Adapt <ChevronDownIcon className="w-4 h-4 ml-1" />
                                        </Button>
                                        {isAdaptMenuOpen && (
                                            <div className="absolute bottom-full mb-2 w-40 bg-white rounded-md shadow-lg border border-slate-200 z-10">
                                                {ALL_PLATFORMS.filter(p => p !== post.platform).map(p => (
                                                    <button
                                                        key={p}
                                                        onClick={() => handleAdaptForPlatform(p)}
                                                        className="w-full text-left px-3 py-2 text-sm hover:bg-slate-50 flex items-center space-x-2 min-h-[44px]"
                                                    >
                                                        <PlatformIcon platform={p} className="w-4 h-4" />
                                                        <span className="capitalize">{p === 'x-twitter' ? 'X' : p}</span>
                                                    </button>
                                                ))}
                                            </div>
                                        )}
                                    </div>
                                    <Button
                                        onClick={() => setDeleteConfirmOpen(true)}
                                        variant="danger_outline"
                                        size="sm"
                                        title="Delete Post"
                                        className="min-h-[44px] min-w-[44px]"
                                    >
                                        <TrashIcon className="w-4 h-4" />
                                    </Button>
                                </div>

                                <div className="flex items-center space-x-2">
                                    <input type="file" ref={uploadInputRef} onChange={handleImageUpload} className="hidden" accept="image/*" />
                                    <Button
                                        onClick={handleCopyCaption}
                                        variant="outline"
                                        size="sm"
                                        title="Copy caption and hashtags"
                                        className="min-h-[44px] min-w-[44px]"
                                    >
                                        <CopyIcon className="w-4 h-4"/>
                                    </Button>
                                    <Button
                                        onClick={handleDownloadImage}
                                        variant="outline"
                                        size="sm"
                                        title="Download image"
                                        disabled={!editablePost.imgSrc}
                                        className="min-h-[44px] min-w-[44px]"
                                    >
                                        <DownloadIcon className="w-4 h-4"/>
                                    </Button>
                                </div>
                            </div>

                            {/* LinkedIn Author Type - Full Width on Mobile */}
                            {post.platform === 'linkedin' && (
                                <div className="w-full">
                                    <select
                                        value={authorType}
                                        onChange={(e) => setAuthorType(e.target.value)}
                                        className="input text-sm w-full min-h-[44px]"
                                    >
                                        <option value="person">Post as Person</option>
                                        {integrations.find(i => i.platform === 'linkedin' && i.companyUrn) && (
                                            <option value="organization">Post as Company</option>
                                        )}
                                    </select>
                                </div>
                            )}
                        </div>

                        {/* Desktop Layout - Original horizontal layout */}
                        <div className="hidden lg:flex items-center justify-between gap-4">
                            <div className="flex items-center space-x-2">
                                <div className="relative">
                                    <Button onClick={() => setAdaptMenuOpen(o => !o)} variant="outline" size="sm" disabled={loadingStates.adapt || !canAdapt}>
                                        Adapt For... <ChevronDownIcon className="w-4 h-4 ml-1" />
                                    </Button>
                                    {isAdaptMenuOpen && (
                                        <div className="absolute bottom-full mb-2 w-40 bg-white rounded-md shadow-lg border border-slate-200 z-10">
                                            {ALL_PLATFORMS.filter(p => p !== post.platform).map(p => (
                                                <button
                                                    key={p}
                                                    onClick={() => handleAdaptForPlatform(p)}
                                                    className="w-full text-left px-3 py-2 text-sm hover:bg-slate-50 flex items-center space-x-2"
                                                >
                                                    <PlatformIcon platform={p} className="w-4 h-4" />
                                                    <span className="capitalize">{p === 'x-twitter' ? 'X' : p}</span>
                                                </button>
                                            ))}
                                        </div>
                                    )}
                                </div>
                                <Button onClick={() => setDeleteConfirmOpen(true)} variant="danger_outline" size="sm" title="Delete Post">
                                    <TrashIcon className="w-4 h-4" />
                                </Button>
                            </div>
                            <div className="flex flex-wrap items-center space-x-2">
                                <input type="file" ref={uploadInputRef} onChange={handleImageUpload} className="hidden" accept="image/*" />
                                <Button onClick={handleCopyCaption} variant="outline" size="sm" title="Copy caption and hashtags">
                                    <CopyIcon className="w-4 h-4"/>
                                    <span className={`ml-2 transition-opacity ${copySuccess ? 'opacity-100' : 'opacity-0'}`}>Copied!</span>
                                </Button>
                                <Button onClick={handleDownloadImage} variant="outline" size="sm" title="Download image" disabled={!editablePost.imgSrc}>
                                    <DownloadIcon className="w-4 h-4"/>
                                </Button>
                                {isDirty && <Button onClick={handleSaveChanges}>Save Changes</Button>}
                                {post.platform === 'linkedin' && (
                                    <div className="flex items-center mr-2">
                                        <select
                                            value={authorType}
                                            onChange={(e) => setAuthorType(e.target.value)}
                                            className="input text-sm"
                                        >
                                            <option value="person">Post as Person</option>
                                            {integrations.find(i => i.platform === 'linkedin' && i.companyUrn) && (
                                                <option value="organization">Post as Company</option>
                                            )}
                                        </select>
                                    </div>
                                )}
                                <Button onClick={handlePostNow} variant="primary" disabled={isPosting}>
                                    {isPosting ? 'Posting...' : 'Post Now'}
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>

                {isDeleteConfirmOpen && (<ConfirmationModal isOpen={isDeleteConfirmOpen} onClose={() => setDeleteConfirmOpen(false)} onConfirm={handleDelete} title="Confirm Post Deletion" confirmText="Yes, Delete Post" size="md"><p className="text-slate-600">Are you sure you want to permanently delete this post? This action cannot be undone.</p></ConfirmationModal>)}
                <style>{`.label { display: block; font-weight: 600; font-size: 0.875rem; color: #334155; margin-bottom: 0.25rem; } .input { display: block; width: 100%; border-radius: 0.375rem; border: 1px solid #e2e8f0; padding: 0.5rem 0.75rem; background-color: white; line-height: 1.5; color: #0f172a; } .input:focus { outline: 2px solid transparent; outline-offset: 2px; border-color: #9333ea; box-shadow: 0 0 0 2px #e9d5ff; } .input:disabled { background-color: #f1f5f9; cursor: not-allowed; } @keyframes fade-in { from { opacity: 0; } to { opacity: 1; } } .animate-fade-in { animation: fade-in 0.3s ease-out; }`}</style>
            </div>
        </Modal>
    );
};

export default PostModal;
