import React from 'react';
import { Post } from '../types';
import PlatformIcon from './ui/PlatformIcon';
import { CheckCircleIcon } from './icons/CheckCircleIcon';
import { format, parseISO } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';

const PostListItem: React.FC<{post: Post; onClick: () => void}> = ({ post, onClick }) => (
    <li className="flex items-center space-x-4 py-3 cursor-pointer hover:bg-slate-50" onClick={onClick}>
        <div className="flex-shrink-0">
            {post.imgSrc ? 
                <img src={post.imgSrc} alt="Post thumbnail" className="w-12 h-12 rounded-md object-cover bg-slate-200"/> :
                <div className="w-12 h-12 rounded-md bg-slate-200 flex items-center justify-center">
                    <PlatformIcon platform={post.platform} className="w-6 h-6 text-slate-400"/>
                </div>
            }
        </div>
        <div className="flex-grow min-w-0">
            <p className="text-sm font-semibold text-slate-800 truncate">{post.caption}</p>
            {post.status === 'published' ? (
                <div className="flex items-center text-xs text-green-600 font-semibold mt-1">
                    <CheckCircleIcon className="w-4 h-4 mr-1.5" />
                    Published
                </div>
            ) : (
                <p className="text-xs text-slate-500">{format(toZonedTime(parseISO(post.scheduledDate!), Intl.DateTimeFormat().resolvedOptions().timeZone), 'p')}</p>
            )}
        </div>
        <div className="flex-shrink-0">
            <PlatformIcon platform={post.platform} className="w-5 h-5 text-slate-500"/>
        </div>
    </li>
);

export default PostListItem;
