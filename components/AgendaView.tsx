import React from 'react';
import { Post } from '../types';
import { CopyIcon } from './icons/CopyIcon';
import { DownloadIcon } from './icons/DownloadIcon';
import Button from './ui/Button';
import PlatformIcon from './ui/PlatformIcon';

interface AgendaViewProps {
    posts: Post[];
    onPostClick: (post: Post) => void;
}

const PostRow: React.FC<{ post: Post; onPostClick: (post: Post) => void; }> = ({ post, onPostClick }) => {
    const handleCopyCaption = (e: React.MouseEvent) => {
        e.stopPropagation();
        navigator.clipboard.writeText(`${post.caption}\n\n${post.hashtags}`);
        // Consider adding a toast notification here from App context if available
    };

    const handleDownloadImage = (e: React.MouseEvent) => {
        e.stopPropagation();
        if (!post.imgSrc) return;
        const link = document.createElement('a');
        link.href = post.imgSrc;
        link.download = `post-${post.id}.jpeg`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    return (
        <div 
            className="flex items-center space-x-4 p-3 bg-white hover:bg-slate-50 transition-colors rounded-lg cursor-pointer border border-transparent hover:border-slate-200"
            onClick={() => onPostClick(post)}
        >
            <div className="flex-shrink-0">
                {post.imgSrc ? (
                    <img src={post.imgSrc} alt="Post thumbnail" className="w-16 h-16 rounded-md object-cover bg-slate-200" />
                ) : (
                    <div className="w-16 h-16 rounded-md bg-slate-200 flex items-center justify-center">
                        <PlatformIcon platform={post.platform} className="w-6 h-6 text-slate-400" />
                    </div>
                )}
            </div>
            <div className="flex-grow min-w-0">
                <div className="flex items-center text-sm">
                    <PlatformIcon platform={post.platform} className="w-4 h-4 text-slate-500 mr-2" />
                    <span className="font-semibold text-slate-800">
                        {new Date(post.scheduledDate!).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </span>
                </div>
                <p className="text-sm text-slate-600 truncate mt-1">{post.caption}</p>
            </div>
            <div className="flex-shrink-0 flex items-center space-x-2">
                <Button onClick={handleCopyCaption} variant="outline" size="sm" title="Copy Caption">
                    <CopyIcon className="w-4 h-4" />
                </Button>
                <Button onClick={handleDownloadImage} variant="outline" size="sm" title="Download Image" disabled={!post.imgSrc}>
                    <DownloadIcon className="w-4 h-4" />
                </Button>
            </div>
        </div>
    );
};

const AgendaView: React.FC<AgendaViewProps> = ({ posts, onPostClick }) => {
    const sortedPosts = [...posts].sort((a, b) => new Date(a.scheduledDate!).getTime() - new Date(b.scheduledDate!).getTime());

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);
    const endOfTomorrow = new Date(tomorrow);
    endOfTomorrow.setDate(tomorrow.getDate() + 1);
    const endOfWeek = new Date(today);
    endOfWeek.setDate(today.getDate() + (7 - today.getDay()) % 7);
    endOfWeek.setHours(23, 59, 59, 999);

    const groups = {
        today: sortedPosts.filter(p => new Date(p.scheduledDate!) >= today && new Date(p.scheduledDate!) < tomorrow),
        tomorrow: sortedPosts.filter(p => new Date(p.scheduledDate!) >= tomorrow && new Date(p.scheduledDate!) < endOfTomorrow),
        thisWeek: sortedPosts.filter(p => new Date(p.scheduledDate!) >= endOfTomorrow && new Date(p.scheduledDate!) <= endOfWeek),
        later: sortedPosts.filter(p => new Date(p.scheduledDate!) > endOfWeek)
    };

    return (
        <div className="max-w-4xl mx-auto space-y-8">
            {Object.entries(groups).map(([key, groupPosts]) => {
                if (groupPosts.length === 0) return null;
                const title = {
                    today: 'Today',
                    tomorrow: 'Tomorrow',
                    thisWeek: 'Later This Week',
                    later: 'Upcoming'
                }[key]!;

                return (
                    <div key={key}>
                        <h2 className="text-lg font-bold text-slate-800 mb-3 px-2">{title}</h2>
                        <div className="space-y-2">
                            {groupPosts.map(post => (
                                <PostRow key={post.id} post={post} onPostClick={onPostClick} />
                            ))}
                        </div>
                    </div>
                )
            })}
             {posts.length === 0 && (
                <div className="text-center py-20 text-slate-500 bg-white rounded-lg border border-dashed border-slate-300">
                    <h3 className="text-lg font-semibold text-slate-700">Your Agenda is Clear</h3>
                    <p className="mt-1">Schedule some posts to see them here.</p>
                </div>
            )}
        </div>
    );
};

export default AgendaView;
