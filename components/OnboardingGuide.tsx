
import React, { useEffect, useState, useLayoutEffect } from 'react';
import { OnboardingGuideProps } from '../types';
import HelperBot from './HelperBot';
import { useUI } from '../contexts/AppContext';

const OnboardingGuide: React.FC<OnboardingGuideProps> = ({ steps, onboardingState, setOnboardingState }) => {
    const [targetRect, setTargetRect] = useState<DOMRect | null>(null);
    const { setView } = useUI();

    const stopTour = () => {
        setOnboardingState({ isActive: false, currentStep: 0 });
        setTargetRect(null);
        document.documentElement.style.setProperty('--highlight-w', `0px`);
    };
    
    const completeTour = () => {
        const lastStep = steps[onboardingState.currentStep];
        if (lastStep?.onCompleteAction?.type === 'navigate') {
            setView(lastStep.onCompleteAction.view);
        }
        stopTour();
    };

    useLayoutEffect(() => {
        if (!onboardingState.isActive) {
            document.documentElement.style.setProperty('--highlight-w', `0px`);
            return;
        }

        const step = steps[onboardingState.currentStep];
        if (!step) {
            stopTour();
            return;
        }

        const targetElement = document.querySelector(`[data-tour-id="${step.elementId}"]`);

        if (targetElement) {
            const rect = targetElement.getBoundingClientRect();
            setTargetRect(rect);
            
            const padding = step.padding ?? 10;
            const docStyle = document.documentElement.style;
            docStyle.setProperty('--highlight-x', `${rect.left - padding}px`);
            docStyle.setProperty('--highlight-y', `${rect.top - padding}px`);
            docStyle.setProperty('--highlight-w', `${rect.width + padding * 2}px`);
            docStyle.setProperty('--highlight-h', `${rect.height + padding * 2}px`);
            docStyle.setProperty('--highlight-r', `${(step.padding ?? 10) + 4}px`);
            
            targetElement.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' });
        } else {
            console.warn(`Onboarding element not found: ${step.elementId}`);
            handleNext();
        }

    }, [onboardingState.isActive, onboardingState.currentStep, steps]);

    const handleNext = () => {
        if (onboardingState.currentStep < steps.length - 1) {
            setOnboardingState(s => ({ ...s, currentStep: s.currentStep + 1 }));
        } else {
            completeTour();
        }
    };

    const handlePrev = () => {
        if (onboardingState.currentStep > 0) {
            setOnboardingState(s => ({ ...s, currentStep: s.currentStep - 1 }));
        }
    };
    
    if (!onboardingState.isActive || !targetRect) {
        return null;
    }

    const currentStepConfig = steps[onboardingState.currentStep];

    return (
        <>
            <div className={`onboarding-overlay active`}></div>
            <HelperBot
                step={currentStepConfig}
                onNext={handleNext}
                onPrev={handlePrev}
                onSkip={stopTour}
                isFirstStep={onboardingState.currentStep === 0}
                isLastStep={onboardingState.currentStep === steps.length - 1}
                targetRect={targetRect}
            />
        </>
    );
};

export default OnboardingGuide;
