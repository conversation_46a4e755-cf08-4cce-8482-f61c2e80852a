

import React, { useMemo } from 'react';
import { ALL_DURATIONS } from '../types';
import Button from './ui/Button';
import GeneratingPlanLoader from './ui/GeneratingPlanLoader';
import { COSTS } from '../contexts/AppContext';
import { RocketIcon } from './icons/RocketIcon';
import Loader from './ui/Loader';
import { useSettings, usePlan } from '../contexts/AppContext';

declare const marked: any;

const durationLabels = { '6m': '6 Months', '1y': '1 Year', '1.5y': '1.5 Years', '2y': '2 Years' };

const GeneratePlanView: React.FC<{ onGeneratePlan: () => Promise<boolean> }> = ({ onGeneratePlan }) => {
    const { settings, loadingStates } = useSettings();
    const { planMarkdown, handlePlanDurationChange } = usePlan();
    
    const { brandVoiceHtml, mainPlanHtml } = useMemo(() => {
        if (!planMarkdown) return { brandVoiceHtml: '', mainPlanHtml: '' };
        const match = planMarkdown.match(/^##\s+/m);
        let brandVoiceMarkdown = planMarkdown, mainPlanMarkdown = '';
        if (match?.index !== undefined) {
            brandVoiceMarkdown = planMarkdown.substring(0, match.index);
            mainPlanMarkdown = planMarkdown.substring(match.index);
        }
        brandVoiceMarkdown = brandVoiceMarkdown.replace(/#.*Brand Voice & Strategic Positioning Guide/i, '').trim();
        try {
            if (typeof marked === 'undefined') return { brandVoiceHtml: 'Loading parser...', mainPlanHtml: 'Loading parser...' };
            return { brandVoiceHtml: marked.parse(brandVoiceMarkdown), mainPlanHtml: marked.parse(mainPlanMarkdown) };
        } catch (e) {
            return { brandVoiceHtml: 'Error parsing brand voice.', mainPlanHtml: 'Error parsing plan.' };
        }
    }, [planMarkdown]);
    
    const canGenerate = settings.role === 'admin' || settings.credits >= COSTS.PLAN_GENERATION;
    const disabledTitle = !canGenerate ? `Insufficient credits. Needs ${COSTS.PLAN_GENERATION}.` : 'Generate your strategic content plan';

    if (loadingStates.plan && !planMarkdown) {
        return <GeneratingPlanLoader settings={settings} />;
    }
    
    if (!planMarkdown && !loadingStates.plan) {
        return (
            <div className="text-center py-20 bg-slate-50/50 rounded-lg border border-dashed border-slate-300 flex flex-col items-center">
                <div className="p-4 bg-purple-100 rounded-full text-purple-600 mb-4"><RocketIcon className="w-10 h-10" /></div>
                <h3 className="text-xl font-bold text-slate-800">Your Strategic Plan Awaits</h3>
                <p className="mt-2 text-slate-500 max-w-md">First, select the duration, then let the AI generate a comprehensive growth strategy.</p>
                <div className="my-6">
                    <label className="text-sm font-semibold text-slate-600 mb-2 block">Choose Plan Duration:</label>
                    <div className="inline-flex items-center bg-white p-1 rounded-lg border border-slate-200 shadow-sm">
                        {ALL_DURATIONS.map(duration => (
                            <Button key={duration} onClick={() => handlePlanDurationChange(duration)} variant={settings.planDuration === duration ? 'accent' : 'ghost'} size="sm" className={`!rounded-md ${settings.planDuration === duration ? 'text-white shadow-sm' : 'text-slate-600 hover:bg-slate-100'}`}>
                                {durationLabels[duration]}
                            </Button>
                        ))}
                    </div>
                </div>
                 <Button onClick={onGeneratePlan} disabled={!canGenerate} variant="primary" title={disabledTitle} className="mt-2">
                    Generate {durationLabels[settings.planDuration]} Plan ({COSTS.PLAN_GENERATION} credits)
                </Button>
            </div>
        );
    }
    
    return (
        <div className="space-y-6">
             {loadingStates.plan && (
                <div className="flex items-center text-sm font-semibold text-purple-600 bg-purple-50 p-3 rounded-lg border border-purple-200 sticky top-0 z-10 backdrop-blur-sm bg-opacity-80">
                    <Loader size="sm" className="mr-3" />
                    Your Co-pilot is writing your plan in real-time...
                </div>
            )}
            {(brandVoiceHtml || mainPlanHtml) && (
                 <div className="bg-white p-6 rounded-lg border border-slate-200 shadow-sm">
                    {brandVoiceHtml && (
                        <>
                            <h2 className="text-2xl font-bold text-slate-800 mb-4">Brand Voice & Strategic Positioning</h2>
                            <div className="prose max-w-none prose-slate" dangerouslySetInnerHTML={{ __html: brandVoiceHtml }} />
                        </>
                    )}
                    {mainPlanHtml && (
                        <div className="prose max-w-none prose-slate mt-6" dangerouslySetInnerHTML={{ __html: mainPlanHtml }} />
                    )}
                </div>
            )}
        </div>
    );
};

export default GeneratePlanView;
