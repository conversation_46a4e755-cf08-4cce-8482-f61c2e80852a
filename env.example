# Supabase
VITE_SUPABASE_URL="your-supabase-url"
VITE_SUPABASE_ANON_KEY="your-supabase-anon-key"

# Stripe
VITE_STRIPE_PUBLISHABLE_KEY="your-stripe-publishable-key"

# Social OAuth Credentials
VITE_META_CLIENT_ID=77ih3hyrj63wus
META_CLIENT_SECRET=WPL_AP1.BAhHkGtHo33ati6a.YFI0/g==

VITE_LINKEDIN_CLIENT_ID="your-linkedin-client-id"
LINKEDIN_CLIENT_SECRET="your-linkedin-client-secret"

VITE_X_CLIENT_ID="your-x-client-id"
X_CLIENT_SECRET="your-x-client-secret"

# Admin Access
ADMIN_CODE="your-secret-admin-code"

# AI Image Providers
# Primary Gemini API key (required for Google Imagen). If unset, app will fall back to free providers.
VITE_API_KEY="your-gemini-api-key"

# Fallback provider order. Comma-separated among: google, pollinations, lexica
# Example below tries Google first, then Pollinations, then Lexica.
VITE_IMAGE_PROVIDER_ORDER="google,pollinations,lexica"

# Optional: Persist generated images to Supabase Storage (recommended for cost optimization)
# When true, selected/generated images are uploaded once to Storage and posts store a small public URL.
VITE_SAVE_IMAGES_TO_SUPABASE=true
# Bucket name for generated images (will be created in Supabase Storage)
VITE_SUPABASE_BUCKET_IMAGES=generated-images
