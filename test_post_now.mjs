import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config({ path: './.env' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase URL or Key is not defined in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testPostNow() {
  console.log('Running test for post-now function...');

  // Simulate a post ID that does not exist to trigger an error
  const testPostId = 99999;

  try {
    const { data, error } = await supabase.functions.invoke('post-now', {
      body: { postId: testPostId },
    });

    if (error) {
      console.error('Function returned an error as expected:');
      console.error(error.message);
    } else {
      console.log('Function returned data:');
      console.log(data);
    }
  } catch (err) {
    console.error('An unexpected error occurred during the test:');
    console.error(err.message);
  }
}

testPostNow();
