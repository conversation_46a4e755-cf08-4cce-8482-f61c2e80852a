import React, { createContext, useState, useContext, ReactNode } from 'react';

interface ModalState {
    isSettingsOpen: boolean;
    isEarlyAdopterModalOpen: boolean;
    isFeedbackModalOpen: boolean;
    isPlanSummaryOpen: boolean;
    isAmbassadorModalOpen: boolean;
    isAskIhabModalOpen: boolean;
    isWelcomeModalOpen: boolean;
    isImageSelectionModalOpen: boolean;
}

interface ModalContextType extends ModalState {
    openModal: (modal: keyof ModalState) => void;
    closeModal: (modal: keyof ModalState) => void;
    setSettingsOpen: (isOpen: boolean) => void;
    setEarlyAdopterModalOpen: (isOpen: boolean) => void;
    setFeedbackModalOpen: (isOpen: boolean) => void;
    setPlanSummaryOpen: (isOpen: boolean) => void;
    setAmbassadorModalOpen: (isOpen: boolean) => void;
    setAskIhabModalOpen: (isOpen: boolean) => void;
    setWelcomeModalOpen: (isOpen: boolean) => void;
    setImageSelectionModalOpen: (isOpen: boolean) => void;
}

const ModalContext = createContext<ModalContextType | undefined>(undefined);

export const ModalProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [modals, setModals] = useState<ModalState>({
        isSettingsOpen: false,
        isEarlyAdopterModalOpen: false,
        isFeedbackModalOpen: false,
        isPlanSummaryOpen: false,
        isAmbassadorModalOpen: false,
        isAskIhabModalOpen: false,
        isWelcomeModalOpen: false,
        isImageSelectionModalOpen: false,
    });

    const openModal = (modal: keyof ModalState) => setModals(prev => ({ ...prev, [modal]: true }));
    const closeModal = (modal: keyof ModalState) => setModals(prev => ({ ...prev, [modal]: false }));

    const setSettingsOpen = (isOpen: boolean) => setModals(prev => ({...prev, isSettingsOpen: isOpen}));
    const setEarlyAdopterModalOpen = (isOpen: boolean) => setModals(prev => ({...prev, isEarlyAdopterModalOpen: isOpen}));
    const setFeedbackModalOpen = (isOpen: boolean) => setModals(prev => ({...prev, isFeedbackModalOpen: isOpen}));
    const setPlanSummaryOpen = (isOpen: boolean) => setModals(prev => ({...prev, isPlanSummaryOpen: isOpen}));
    const setAmbassadorModalOpen = (isOpen: boolean) => setModals(prev => ({...prev, isAmbassadorModalOpen: isOpen}));
    const setAskIhabModalOpen = (isOpen: boolean) => setModals(prev => ({...prev, isAskIhabModalOpen: isOpen}));
    const setWelcomeModalOpen = (isOpen: boolean) => setModals(prev => ({...prev, isWelcomeModalOpen: isOpen}));
    const setImageSelectionModalOpen = (isOpen: boolean) => setModals(prev => ({...prev, isImageSelectionModalOpen: isOpen}));

    return (
        <ModalContext.Provider value={{
            ...modals,
            openModal,
            closeModal,
            setSettingsOpen,
            setEarlyAdopterModalOpen,
            setFeedbackModalOpen,
            setPlanSummaryOpen,
            setAmbassadorModalOpen,
            setAskIhabModalOpen,
            setWelcomeModalOpen,
            setImageSelectionModalOpen,
        }}>
            {children}
        </ModalContext.Provider>
    );
};

export const useModals = () => {
    const context = useContext(ModalContext);
    if (context === undefined) {
        throw new Error('useModals must be used within a ModalProvider');
    }
    return context;
};