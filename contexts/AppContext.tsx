import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import { Post, Settings, BackupData, View, WeekData, Platform, GeneratedPostContent, PostVariation, CreditTransaction, ToastMessage, PlanDuration, CreditPack, SettingsTab, ImageVariationOptions, OnboardingState, OnboardingStep, Integration } from '../types';
import { getMetaAuthUrl } from '../integrations/meta/auth';
import { getLinkedInAuthUrl } from '../integrations/linkedin/auth';
import { getXAuthUrl } from '../integrations/x/auth';
import { generateContentPlanStream, generateImage, improvePost, adaptPostForPlatform, generatePostVariations, refineContentPlan } from '../services/geminiService';
import { parsePlanToHierarchy } from '../utils/planParser';
import { supabase } from '../services/supabaseClient';
import type { User } from '@supabase/supabase-js';

import { saveImageDataUrlToSupabase, isHttpUrl } from '../services/imageStorage';
import { DEMO_POSTS, DEMO_PLAN, DEMO_SETTINGS } from '../data/demoContent';
// --- CONSTANTS ---
const COSTS = {
    PLAN_GENERATION: 50,
    PLAN_REFINEMENT: 15,
    WEEKLY_POST_BATCH: 25,
    POST_IMPROVEMENT: 5,
    POST_ADAPTATION: 5,
    IMAGE_GENERATION_PER_IMAGE: 2,
    IMAGE_VARIATION_GENERATION: 4,
    FIRST_MONTH_BATCH: 100,
};

const PROMO_CODE_PIONEER = 'PULSECRAFTPIONEER';

const getOnboardingSteps = (brandName: string): OnboardingStep[] => ([
    { elementId: 'nav-dashboard', title: `Welcome, ${brandName || 'Creator'}!`, text: "This is your dashboard, a quick overview of your content schedule and key stats.", position: 'right', padding: 10 },
    { elementId: 'nav-strategy', title: 'The Strategy Brief', text: `This is where the magic happens. You’ll generate your AI-powered strategy for **${brandName}** here first.`, position: 'right', padding: 10 },
    { elementId: 'nav-planner', title: 'The Content Planner', text: 'Once posts are generated, they appear here. You can view them in a grid, calendar, or agenda format.', position: 'right', padding: 10 },
    { elementId: 'nav-settings', title: 'Settings & Perks', text: "Here you can fine-tune the AI, manage your account, and find your early adopter perks!", position: 'right', padding: 10 },
    {
        elementId: 'nav-strategy',
        title: "You're Ready!",
        text: "That's the grand tour! We'll drop you off at the Strategy Brief so you can generate your first plan.",
        position: 'right',
        padding: 10,
        onCompleteAction: { type: 'navigate', view: 'strategy' }
    },
]);


// --- CONTEXT DEFINITIONS ---
interface ISettingsContext {
    settings: Settings;
    loadingStates: any;
    handleSaveSettings: (updates: Partial<Omit<Settings, 'role' | 'creditHistory' | 'credits'>>) => Promise<void>;
    handleAdminAccess: (code: string) => void;
    handlePurchaseCredits: (pack: CreditPack) => void;
    handleApplyPromoCode: (code: string) => void;
    checkCredits: (cost: number) => boolean;
}

interface IPostsContext {
    posts: Post[];
    imageGenAttemptedForPost: Set<number>;
    setImageGenAttemptedForPost: React.Dispatch<React.SetStateAction<Set<number>>>;
    handleUpdatePost: (updatedPost: Post) => Promise<void>;
    handleDeletePost: (postId: number) => Promise<void>;
    handleAddNewPost: (newPost: Omit<Post, 'id'>) => Promise<void>;
    handleGenerateImageForPost: (post: Post, imagePrompt: string, count: number, styleOverride?: string) => Promise<void>;
    handleImprovePost: (post: Post, request: string) => Promise<GeneratedPostContent | null>;
    handleAdaptPost: (post: Post, targetPlatform: Platform) => Promise<void>;
}

interface IPlanContext {
    planMarkdown: string;
    completedWeeks: string[];
    handleGeneratePlan: () => Promise<boolean>;
    handleRefinePlan: (refinementInput: string) => Promise<{ newPlan: string; diff: string } | null>;
    handleGenerateWeeklyBatch: (weekId: string, weekData: WeekData, flatWeekIds: string[], instructions?: string) => Promise<void>;
    handleGenerateFirstMonth: () => Promise<boolean>;
    handlePlanUpdate: (newPlan: string) => Promise<void>;
    handlePlanDurationChange: (duration: PlanDuration) => Promise<void>;
}

interface IUIContext {
    view: View;
    toasts: ToastMessage[];
    selectedPost: Post | null;
    isSettingsOpen: boolean;
    isEarlyAdopterModalOpen: boolean;
    isFeedbackModalOpen: boolean;
    isPlanSummaryOpen: boolean;
    isAmbassadorModalOpen: boolean;
    isAskIhabModalOpen: boolean;
    isWelcomeModalOpen: boolean;
    imageSelectionOptions: ImageVariationOptions;
    isSubmittingFeedback: boolean;
    onboardingState: OnboardingState;
    isAppLoading: boolean;
    initialSettingsTab: SettingsTab | null;
    isProfileSetupNeeded: boolean;
    setView: React.Dispatch<React.SetStateAction<View>>;
    addToast: (message: string, type?: 'success' | 'error' | 'info', action?: { label: string; onClick: () => void; }) => void;
    setSelectedPost: React.Dispatch<React.SetStateAction<Post | null>>;
    setSettingsOpen: React.Dispatch<React.SetStateAction<boolean>>;
    setFeedbackModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
    setPlanSummaryOpen: React.Dispatch<React.SetStateAction<boolean>>;
    setAmbassadorModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
    setAskIhabModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
    setWelcomeModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
    setEarlyAdopterModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
    setOnboardingState: React.Dispatch<React.SetStateAction<OnboardingState>>;
    setToasts: React.Dispatch<React.SetStateAction<ToastMessage[]>>;
    setImageSelectionOptions: React.Dispatch<React.SetStateAction<ImageVariationOptions>>;
    setInitialSettingsTab: React.Dispatch<React.SetStateAction<SettingsTab | null>>;
}

interface IIntegrationsContext {
    integrations: Integration[];
    handleConnectPlatform: (platform: Platform) => Promise<void>;
    handleDisconnectPlatform: (platform: Platform) => Promise<void>;
}

interface IAppContext {
    handleLogout: () => Promise<void>;
    handleExportData: () => void;
    handleImportData: (data: any) => void;
    handleResetApp: () => Promise<void>;
    handleSubmitFeedback: (feedbackType: string, message: string) => Promise<void>;
    handleStartTour: () => void;
    handleSkipOnboarding: () => Promise<void>;
    handleCompleteOnboarding: (onboardingData: { brandName: string; businessContext: string; }) => Promise<void>;
    ONBOARDING_STEPS: OnboardingStep[];
    user: User | null;
}

const SettingsContext = createContext<ISettingsContext | undefined>(undefined);
const PostsContext = createContext<IPostsContext | undefined>(undefined);
const PlanContext = createContext<IPlanContext | undefined>(undefined);
const UIContext = createContext<IUIContext | undefined>(undefined);
const IntegrationsContext = createContext<IIntegrationsContext | undefined>(undefined);
const AppContext = createContext<IAppContext | undefined>(undefined);

// --- PROVIDER COMPONENT ---
const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    // --- STATE ---
    const [user, setUser] = useState<User | null>(null);
    const [view, setView] = useState<View>('dashboard');
    const [isDemoMode, setIsDemoMode] = useState(false);
    const [posts, setPosts] = useState<Post[]>([]);
    const [settings, setSettings] = useState<Settings>({
        userId: '',
        credits: 0, role: 'user', creditHistory: [], showComingSoon: false, businessContext: '', brandName: '',
        bio: '', profileImageUrl: '', websiteUrl: '', generationLanguage: 'en', characterDescription: '',
        styleDescription: '', strategyFramework: 'Integrated Multi-Expert Framework', platforms: ['instagram'], planDuration: '6m',
        integrations: [],
        defaultLinkedInAuthorType: 'person',
    });
    const [integrations, setIntegrations] = useState<Integration[]>([]);
    const [onboardingData, setOnboardingData] = useState({ hasOnboarded: false, isProfileSetupNeeded: false, ambassadorModalShown: false, pioneerPromoRedeemed: false });
    const [planMarkdown, setPlanMarkdown] = useState<string>('');
    const [completedWeeks, setCompletedWeeks] = useState<string[]>([]);

    const [selectedPost, setSelectedPost] = useState<Post | null>(null);
    const [isSettingsOpen, setSettingsOpen] = useState(false);
    const [isEarlyAdopterModalOpen, setEarlyAdopterModalOpen] = useState(false);
    const [isFeedbackModalOpen, setFeedbackModalOpen] = useState(false);
    const [isPlanSummaryOpen, setPlanSummaryOpen] = useState(false);
    const [isAmbassadorModalOpen, setAmbassadorModalOpen] = useState(false);
    const [isAskIhabModalOpen, setAskIhabModalOpen] = useState(false);
    const [isWelcomeModalOpen, setWelcomeModalOpen] = useState(false);
    const [toasts, setToasts] = useState<ToastMessage[]>([]);
    const [isAppLoading, setAppLoading] = useState(true);
    const [initialSettingsTab, setInitialSettingsTab] = useState<SettingsTab | null>(null);
    const [imageSelectionOptions, setImageSelectionOptions] = useState<ImageVariationOptions>({ isOpen: false, isGenerating: false, images: [], onSelect: () => {} });
    const [isSubmittingFeedback, setIsSubmittingFeedback] = useState(false);
    const [onboardingState, setOnboardingState] = useState<OnboardingState>({ isActive: false, currentStep: 0 });
    const [imageGenAttemptedForPost, setImageGenAttemptedForPost] = useState<Set<number>>(new Set());

    const [loadingStates, setLoadingStates] = useState({
        plan: false, refine: false, weeklyBatch: null as string | null, firstMonth: false, improve: false, adapt: false, image: null as number | null,
    });

    const ONBOARDING_STEPS = getOnboardingSteps(settings.brandName);

    // --- TOASTS ---
    const addToast = useCallback((message: string, type: 'success' | 'error' | 'info' = 'info', action?: { label: string; onClick: () => void; }) => {
        const id = Date.now();
        setToasts(prev => [...prev, { id, message, type, action }]);
        setTimeout(() => setToasts(prev => prev.filter(toast => toast.id !== id)), 5000);
    }, []);

    const handleOpenBuyCredits = () => {
        setInitialSettingsTab('Buy Credits');
        setSettingsOpen(true);
    };

    const updateAppDataInDb = async (dataToUpdate: any) => {
        if (!user) return;
        // Stringifying and parsing the data is a workaround to prevent a TypeScript error
        // with deep/recursive types when updating a jsonb column in Supabase.
        const { error } = await supabase.from('profiles').update({ app_data: JSON.parse(JSON.stringify(dataToUpdate)) } as any).eq('id', user.id);
        if (error) {
            console.error("Failed to save data to Supabase:", error);
            addToast("Error: Could not save your changes to the cloud.", 'error');
        }
    };

    // --- USEEFFECTS for Initialization and DB Sync ---
    useEffect(() => {
        // This effect handles the OAuth callback from the popup.
        if (window.opener && window.location.search.includes('code=')) {
            const params = new URLSearchParams(window.location.search);
            window.opener.postMessage({
                type: 'oauth-callback',
                data: {
                    search: window.location.search,
                    hash: window.location.hash,
                    params: Object.fromEntries(params)
                }
            }, window.location.origin);
            window.close();
            return; // Do not proceed with app initialization in the popup
        }

        // Check for demo mode
        const demoMode = localStorage.getItem('pulsecraft_demo_mode') === 'true';
        if (demoMode) {
            setIsDemoMode(true);
            // Initialize with demo data
            setPosts(DEMO_POSTS);
            setSettings(DEMO_SETTINGS);
            setPlanMarkdown(DEMO_PLAN);
            setAppLoading(false);
            return;
        }

        const fetchUserData = async () => {
            const { data: { user } } = await supabase.auth.getUser();
            if (user) {
                setUser(user);
                let { data: profile, error } = await supabase.from('profiles').select('app_data').eq('id', user.id).single();
                // If no profile row exists, create it!
                if (!profile) {
                    await supabase.from('profiles').insert({ id: user.id, app_data: {} });
                    // Try fetching again
                    const result = await supabase.from('profiles').select('app_data').eq('id', user.id).single();
                    profile = result.data;
                }
                if (!profile) {
                    setAppLoading(false);
                    return;
                }
                const appData = profile.app_data as any;
                if (appData?.settings) {
                    setSettings({ ...settings, ...appData.settings, userId: user.id });
                } else {
                    setSettings(s => ({...s, userId: user.id}));
                }
                setPosts(appData?.posts || []);
                setPlanMarkdown(appData?.planMarkdown || '');
                setCompletedWeeks(appData?.completedWeeks || []);
                setIntegrations(appData?.integrations || []);
                if(appData?.onboardingData) {
                    setOnboardingData(appData.onboardingData);
                }
                if (!appData?.onboardingData?.hasOnboarded) {
                    setEarlyAdopterModalOpen(true);
                }
            } else {
                 setAppLoading(false);
            }
        };
        fetchUserData().finally(() => setAppLoading(false));
    }, []);

    const saveData = useCallback(async () => {
        if (isAppLoading || !user) return; // Don't save during initial load
        const backupData = {
            settings,
            // Persist imgSrc only if it's a URL (avoid storing large base64 data URLs in DB)
            posts: posts.map(p => {
                const { imgSrc, ...rest } = p as any;
                return isHttpUrl(imgSrc) ? { ...rest, imgSrc } : rest;
            }),
            planMarkdown,
            completedWeeks,
            onboardingData,
            integrations,
        };
        await updateAppDataInDb(backupData);
    }, [isAppLoading, user, settings, posts, planMarkdown, completedWeeks, onboardingData, integrations]);

    useEffect(() => {
        const handler = setTimeout(() => {
            saveData();
        }, 1500); // Debounce save operations
        return () => clearTimeout(handler);
    }, [saveData]);

    // Low credit check
    useEffect(() => {
        if (settings.role === 'admin' || !user) return;
        const lowCreditThreshold = 250;
        if (settings.credits < lowCreditThreshold && !onboardingData.ambassadorModalShown) {
            setAmbassadorModalOpen(true);
            setOnboardingData(d => ({ ...d, ambassadorModalShown: true }));
        }
    }, [settings.credits, user, settings.role, onboardingData.ambassadorModalShown]);

    // --- CORE HANDLERS ---
    const checkCredits = (cost: number) => {
        if (settings.role === 'admin') return true;
        if (settings.credits >= cost) return true;
        addToast(`Insufficient credits. This action costs ${cost}, but you only have ${settings.credits}.`, 'error', { label: 'Buy Credits', onClick: handleOpenBuyCredits });
        return false;
    };

    const modifyCredits = (amount: number, description: string) => {
        if (settings.role === 'admin' && amount < 0) return; // Admins have infinite, don't deduct

        const newTransaction: CreditTransaction = { timestamp: new Date().toISOString(), description, amount };
        setSettings(prev => ({
            ...prev,
            credits: Math.max(0, prev.credits + amount),
            creditHistory: [newTransaction, ...prev.creditHistory].slice(0, 100),
        }));
    };

    // Settings Handlers
    const handleSaveSettings = async (updates: Partial<Omit<Settings, 'role' | 'creditHistory' | 'credits'>>) => {
        if(updates.platforms && updates.platforms.length === 0) {
            addToast("Please select at least one target platform.", 'error'); return;
        }
        setSettings(prev => ({ ...prev, ...updates }));
        addToast("Settings saved!", 'success');
        setSettingsOpen(false);
    };

    const handleAdminAccess = (code: string) => {
        if (code === (process.env.ADMIN_CODE || '123456')) {
            setSettings(prev => ({ ...prev, role: 'admin' }));
            addToast('Admin access granted! You now have unlimited credits.', 'success');
        } else {
            addToast('Incorrect admin code.', 'error');
        }
    };

    const handlePurchaseCredits = (pack: CreditPack) => {
        modifyCredits(pack.credits, `Purchased ${pack.name}`);
        addToast(`Successfully added ${pack.credits.toLocaleString()} credits to your account!`, 'success');
    };

    const handleApplyPromoCode = async (code: string) => {
        if (code.trim().toUpperCase() === PROMO_CODE_PIONEER) {
            if (onboardingData.pioneerPromoRedeemed) {
                addToast("This promo code has already been redeemed.", 'error'); return;
            }
            const bonusCredits = 1250;
            modifyCredits(bonusCredits, `Redeemed Pioneer promo code`);
            setOnboardingData(d => ({ ...d, pioneerPromoRedeemed: true }));
            addToast(`Success! ${bonusCredits.toLocaleString()} bonus credits added.`, 'success');
        } else {
            addToast('Invalid promo code.', 'error');
        }
    };

    // Post Handlers
    const handleUpdatePost = async (updatedPost: Post) => {
        setPosts(prev => prev.map(p => p.id === updatedPost.id ? updatedPost : p));
        if (selectedPost && selectedPost.id === updatedPost.id) setSelectedPost(updatedPost);
    };

    const handleDeletePost = async (postId: number) => {
        setPosts(prev => prev.filter(p => p.id !== postId));
        if (selectedPost && selectedPost.id === postId) setSelectedPost(null);
        addToast(`Post #${postId} has been deleted.`, 'success');
    };

    const handleAddNewPost = async (newPost: Omit<Post, 'id'>) => {
        setPosts(prev => {
            const nextId = prev.length > 0 ? Math.max(...prev.map(p => p.id)) + 1 : 1;
            return [{ ...newPost, id: nextId }, ...prev];
        });
        addToast(`New post for ${newPost.platform} has been added!`, 'success');
    };

    // Plan Handlers
    const handlePlanUpdate = async (newPlan: string) => {
        setPlanMarkdown(newPlan);
    };

    const handlePlanDurationChange = async (duration: PlanDuration) => {
        await handleSaveSettings({ planDuration: duration });
    };

    // --- Integration Handlers ---
    const handleConnectPlatform = async (platform: Platform) => {
        // LinkedIn is particular about the redirect URI matching exactly, including the trailing slash.
        const redirectUri = platform === 'linkedin'
            ? `${window.location.origin}/`
            : window.location.origin;
        const state = `${platform}::${Math.random().toString(36).substring(2)}`;
        localStorage.setItem('oauth_state', state);

        let authUrl = '';
        try {
            switch (platform) {
                case 'linkedin':
                    authUrl = getLinkedInAuthUrl(redirectUri, state);
                    break;
                case 'x-twitter':
                    authUrl = getXAuthUrl(redirectUri, state);
                    break;
                case 'facebook':
                case 'instagram':
                    authUrl = getMetaAuthUrl(redirectUri, state);
                    break;
                default:
                    throw new Error('Unsupported platform');
            }
        } catch (error: any) {
            addToast(`Error generating auth URL for ${platform}: ${error.message}`, 'error');
            return;
        }

        const popup = window.open(authUrl, 'oauth-popup', 'width=600,height=700');

        const handleMessage = async (event: MessageEvent) => {
            if (event.origin !== window.location.origin || !event.data || event.data.type !== 'oauth-callback') {
                return;
            }

            if (popup) popup.close();
            window.removeEventListener('message', handleMessage);

            const { params } = event.data.data;
            const code = params?.code;
            const returnedState = params?.state;
            const storedState = localStorage.getItem('oauth_state');

            if (returnedState !== storedState) {
                addToast('OAuth state mismatch. Possible CSRF attack.', 'error');
                localStorage.removeItem('oauth_state');
                return;
            }
            localStorage.removeItem('oauth_state');

            const [oauthPlatform] = returnedState.split('::');

            if (code) {
                try {
                    const functionName = 'exchange-oauth-code'; // A single function to handle all
                    const edgeFunctionUrl = `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/${functionName}`;

                    const session = await supabase.auth.getSession();
                    const jwt = session?.data?.session?.access_token;

                    const response = await fetch(edgeFunctionUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            ...(jwt ? { Authorization: `Bearer ${jwt}` } : {})
                        },
                        body: JSON.stringify({ code, platform: oauthPlatform, redirectUri })
                    });

                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.error || 'Token exchange failed');
                    }

                    const integrationData: Integration = await response.json();

                    // Persist integration for scheduler/post-now usage
                    try {
                        if (!user) throw new Error('User not found in session');
                        await supabase
                            .from('integrations')
                            .upsert({
                                user_id: user.id,
                                platform: oauthPlatform,
                                access_token: (integrationData as any).accessToken,
                                refresh_token: (integrationData as any).refreshToken,
                                expires_at: (integrationData as any).expiresAt ?? null,
                                scopes: (integrationData as any).scopes ?? [],
                                authorUrn: (integrationData as any).authorUrn ?? null,
                                companyUrn: (integrationData as any).companyUrn ?? null
                            }, { onConflict: 'user_id,platform' });
                    } catch (dbErr: any) {
                        console.warn('Failed to persist integration to DB:', dbErr?.message || dbErr);
                    }

                    // Persist in app state as well (used by the UI)
                    setIntegrations(prev => [...prev.filter(i => i.platform !== platform), integrationData]);
                    addToast(`Successfully connected to ${platform}!`, 'success');

                } catch (err: any) {
                    addToast(`Failed to connect to ${platform}: ${err.message}`, 'error');
                }
            } else {
                addToast(`Authentication with ${platform} failed. No code returned.`, 'error');
            }
        };

        window.addEventListener('message', handleMessage);
    };

    const handleDisconnectPlatform = async (platform: Platform) => {
        setIntegrations(prev => prev.filter(i => i.platform !== platform));
        addToast(`Disconnected from ${platform}.`, 'success');
    };


    // App Handlers
    const handleLogout = async () => {
        await supabase.auth.signOut();
        // The onAuthStateChange listener will handle the UI update
    };

    const handleCompleteOnboarding = async (onboardingData: { brandName: string; businessContext: string; }) => {
        await handleSaveSettings({ brandName: onboardingData.brandName, businessContext: onboardingData.businessContext });
        setOnboardingData(d => ({...d, hasOnboarded: true, isProfileSetupNeeded: false}));
        setEarlyAdopterModalOpen(false);
    };

    const handleSkipOnboarding = async () => {
        setOnboardingData(d => ({...d, hasOnboarded: true, isProfileSetupNeeded: true}));
        setEarlyAdopterModalOpen(false);
        addToast("You can set up your brand in Settings anytime!", 'info');
    };

    const handleStartTour = () => {
        setEarlyAdopterModalOpen(false);
        setOnboardingState({ isActive: true, currentStep: 0 });
    };

    const handleSubmitFeedback = async (feedbackType: string, message: string) => {
        setIsSubmittingFeedback(true);
        try {
            const userEmail = user?.email || '';
            const userName = settings.brandName || '';
            const feedbackUrl = import.meta.env.MODE === 'development'
                ? 'http://localhost:54321/functions/v1/send-feedback-email'
                : 'https://tsvfxetwlkfvcdviuuzb.functions.supabase.co/send-feedback-email';
            // Get the Supabase JWT if available
            const session = await supabase.auth.getSession();
            const jwt = session?.data?.session?.access_token;
            const response = await fetch(feedbackUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...(jwt ? { Authorization: `Bearer ${jwt}` } : {})
                },
                body: JSON.stringify({ userEmail, userName, feedbackType, message })
            });
            const result = await response.json();
            if (!response.ok) throw new Error(result.error || 'Failed to send feedback email.');
            addToast('Thank you! Your feedback has been sent and logged.', 'success');
            setFeedbackModalOpen(false);
        } catch (err: any) {
            addToast(`Error sending feedback: ${err.message || err}`, 'error');
        } finally {
            setIsSubmittingFeedback(false);
        }
    };

    const handleExportData = () => {
        const backupData = { settings, posts: posts.map(({ imgSrc, ...rest }) => rest), planMarkdown, completedWeeks, onboardingData, integrations };
        const blob = new Blob([JSON.stringify(backupData, null, 2)], { type: "application/json" });
        const a = document.createElement("a");
        a.href = URL.createObjectURL(blob);
        a.download = "pulsecraft_backup.json";
        a.click();
        URL.revokeObjectURL(a.href);
        addToast('Data exported successfully!', 'success');
    };

    const handleImportData = (data: any) => {
        if(window.confirm("Importing data will overwrite your current work. Continue?")) {
            setSettings(data.settings);
            setPosts(data.posts);
            setPlanMarkdown(data.planMarkdown);
            setCompletedWeeks(data.completedWeeks);
            setOnboardingData(data.onboardingData);
            setIntegrations(data.integrations || []);
            addToast("Data imported successfully!", 'success');
        }
    };

    const handleResetApp = async () => {
        if (window.confirm("This will delete all content and progress. Account settings and credits will be kept. This is irreversible. Continue?")) {
            setPosts([]);
            setPlanMarkdown('');
            setCompletedWeeks([]);
            setIntegrations([]);
            addToast('All content has been reset.', 'success');
        }
    };

    // --- Centralized AI Action Handlers ---
    const handleGeneratePlan = async (): Promise<boolean> => {
        if (!checkCredits(COSTS.PLAN_GENERATION)) return false;
        setPlanMarkdown('');
        setLoadingStates(s => ({...s, plan: true}));
        modifyCredits(-COSTS.PLAN_GENERATION, `Generated ${settings.planDuration} strategy plan`);
        try {
            const stream = generateContentPlanStream(settings);
            let fullPlan = '';
            for await (const chunk of stream) {
                fullPlan += chunk;
                setPlanMarkdown(fullPlan);
            }
            addToast('Strategy plan generated successfully!', 'success');
            return true;
        } catch (e) {
            modifyCredits(COSTS.PLAN_GENERATION, 'Failed plan generation - Refund');
            addToast(`Plan generation failed. Credits refunded. Error: ${e instanceof Error ? e.message : 'Unknown error'}`, 'error');
            return false;
        } finally {
            setLoadingStates(s => ({...s, plan: false}));
        }
    };

    const handleRefinePlan = async (refinementInput: string): Promise<{ newPlan: string; diff: string } | null> => {
        if (!checkCredits(COSTS.PLAN_REFINEMENT)) return null;
        setLoadingStates(s => ({...s, refine: true}));
        modifyCredits(-COSTS.PLAN_REFINEMENT, `Refined plan`);
        try {
            const result = await refineContentPlan(settings, planMarkdown, refinementInput);
if (!result || !result.newPlan) throw new Error("AI did not return a valid refinement.");
            addToast("Plan refinement successful!", 'success');
            return result;
        } catch (e) {
            modifyCredits(COSTS.PLAN_REFINEMENT, `Failed plan refinement - Refund`);
            addToast(`Plan refinement failed. Credits refunded. Error: ${e instanceof Error ? e.message : 'Unknown error'}`, 'error');
            return null;
        } finally {
            setLoadingStates(s => ({...s, refine: false}));
        }
    };

    const handleGenerateWeeklyBatch = async (weekId: string, weekData: WeekData, flatWeekIds: string[], instructions?: string): Promise<void> => {
        if (!checkCredits(COSTS.WEEKLY_POST_BATCH)) return;
        setLoadingStates(s => ({ ...s, weeklyBatch: weekId }));
        modifyCredits(-COSTS.WEEKLY_POST_BATCH, `Generated posts for ${weekId.split(' | ').pop()}`);

        try {
            let allNewPosts: Omit<Post, 'id'>[] = [];
            for (const postIdea of weekData.postIdeas) {
                const variations = await generatePostVariations(settings, weekData, postIdea, instructions);
                if (!variations) continue; // Or throw an error if you expect variations for every idea

                const lastScheduledDate = posts.reduce((latest, post) => (post.scheduledDate && new Date(post.scheduledDate) > latest ? new Date(post.scheduledDate) : latest), new Date(0));
                const scheduleStartDate = new Date(Math.max(new Date().getTime(), lastScheduledDate.getTime()));
                scheduleStartDate.setDate(scheduleStartDate.getDate() + 1);

                const newPostsForIdea = variations.map((variation, i) => {
                    const scheduleDate = new Date(scheduleStartDate);
                    scheduleDate.setDate(scheduleDate.getDate() + i * 2); // Stagger posts
                    scheduleDate.setHours(9 + Math.floor(Math.random() * 9));
                    return {
                        platform: variation.platform,
                        caption: variation.caption,
                        hashtags: variation.hashtags,
                        image_prompt: variation.image_prompt,
                        likes: Math.floor(Math.random() * 1000),
                        comments: Math.floor(Math.random() * 100),
                        date: new Date().toISOString(),
                        scheduledDate: scheduleDate.toISOString(),
                        weekId: weekId,
                        status: 'scheduled' as const,
                    };
                });
                allNewPosts.push(...newPostsForIdea);
            }

            if (allNewPosts.length === 0) {
                throw new Error("AI did not return any post variations for any idea.");
            }

            setPosts(prev => {
                const nextId = prev.length > 0 ? Math.max(...prev.map(p => p.id)) + 1 : 1;
                return [...prev, ...allNewPosts.map((p, i) => ({ ...p, id: nextId + i }))];
            });
            setCompletedWeeks(prev => [...new Set([...prev, weekId])]);
            addToast(`${allNewPosts.length} posts generated for "${weekId.split(' | ').pop()}"`, 'success');

        } catch (e) {
            modifyCredits(COSTS.WEEKLY_POST_BATCH, `Failed batch generation for ${weekId} - Refund`);
            addToast(`Post generation failed. Credits refunded. Error: ${e instanceof Error ? e.message : 'Unknown error'}`, 'error');
        } finally {
            setLoadingStates(s => ({ ...s, weeklyBatch: null }));
        }
    };

    const handleGenerateFirstMonth = async (): Promise<boolean> => {
        if (!checkCredits(COSTS.FIRST_MONTH_BATCH)) return false;

        setLoadingStates(s => ({ ...s, firstMonth: true }));
        modifyCredits(-COSTS.FIRST_MONTH_BATCH, 'Generated first month of content');

        try {
            const hierarchy = parsePlanToHierarchy(planMarkdown);
            const allWeeks = Object.values(hierarchy).flatMap(y => Object.values(y)).flatMap(q => Object.values(q)).flatMap(m => Object.entries(m).map(([weekId, weekData]) => ({weekId, weekData})));
            const weeksToGenerate = allWeeks.filter(w => !completedWeeks.includes(w.weekId)).slice(0, 4);

            if (weeksToGenerate.length === 0) {
                addToast("All available weeks have been generated.", 'info');
                modifyCredits(COSTS.FIRST_MONTH_BATCH, 'No new weeks to generate - Refund');
                setLoadingStates(s => ({ ...s, firstMonth: false }));
                return true;
            }

            for (const { weekId, weekData } of weeksToGenerate) {
                await handleGenerateWeeklyBatch(weekId, weekData, []);
            }

            addToast(`Successfully generated content for the first ${weeksToGenerate.length} weeks!`, 'success');
            return true;

        } catch (e) {
            modifyCredits(COSTS.FIRST_MONTH_BATCH, 'Failed first month generation - Refund');
            addToast(`Post generation failed. Credits refunded. Error: ${e instanceof Error ? e.message : 'Unknown error'}`, 'error');
            return false;
        } finally {
            setLoadingStates(s => ({ ...s, firstMonth: false }));
        }
    };

    const handleGenerateImageForPost = async (post: Post, imagePrompt: string, count: number, styleOverride?: string): Promise<void> => {
        const cost = COSTS.IMAGE_GENERATION_PER_IMAGE * count;
        if (!checkCredits(cost)) return;

        setLoadingStates(s => ({ ...s, image: post.id }));
        if (count > 1) setImageSelectionOptions({ isOpen: true, isGenerating: true, images: [], onSelect: () => {} });

        // Deduct upfront; may refund below for placeholder results
        modifyCredits(-cost, `Generated ${count} image(s) for post #${post.id}`);

        try {
            const result = await generateImage(imagePrompt, settings, count, styleOverride);
            if (!result || result.length === 0) throw new Error("AI did not return any images.");

            const isPlaceholder = (url: string) => url.startsWith('data:image/svg+xml');
            const allPlaceholders = result.every(isPlaceholder);
            const shouldUpload = (import.meta as any).env?.VITE_SAVE_IMAGES_TO_SUPABASE === 'true';

            if (count === 1) {
                let finalSrc = result[0];

                // Upload to Supabase Storage if configured, not a placeholder, and is a data URL
                if (!allPlaceholders && shouldUpload && finalSrc.startsWith('data:image/')) {
                    const uploadedUrl = await saveImageDataUrlToSupabase(finalSrc, {
                        userId: settings.userId,
                        postId: post.id,
                        filenameHint: `${settings.brandName}-${post.platform}`
                    });
                    if (uploadedUrl) finalSrc = uploadedUrl;
                }

                await handleUpdatePost({ ...post, imgSrc: finalSrc });
                if (allPlaceholders) {
                    modifyCredits(cost, `Image providers unavailable - Placeholder used - Refund`);
                    addToast("Image providers are currently unavailable. A placeholder image was used and your credits were refunded.", 'info');
                } else {
                    addToast("Image generated successfully!", 'success');
                }
            } else {
                if (allPlaceholders) {
                    // Close modal if we showed a generating state, set placeholder, and refund
                    setImageSelectionOptions(prev => ({ ...prev, isOpen: false, isGenerating: false }));
                    await handleUpdatePost({ ...post, imgSrc: result[0] });
                    modifyCredits(cost, `Image providers unavailable - Placeholder used - Refund`);
                    addToast("Image providers are currently unavailable. A placeholder image was used and your credits were refunded.", 'info');
                } else {
                    setImageSelectionOptions({
                        isOpen: true,
                        isGenerating: false,
                        images: result,
                        onSelect: async (imgSrc: string) => {
                            let finalSrc = imgSrc;
                            if (shouldUpload && !isPlaceholder(imgSrc) && imgSrc.startsWith('data:image/')) {
                                const uploadedUrl = await saveImageDataUrlToSupabase(imgSrc, {
                                    userId: settings.userId,
                                    postId: post.id,
                                    filenameHint: `${settings.brandName}-${post.platform}`
                                });
                                if (uploadedUrl) finalSrc = uploadedUrl;
                            }
                            await handleUpdatePost({ ...post, imgSrc: finalSrc });
                            setImageSelectionOptions(prev => ({ ...prev, isOpen: false }));
                            addToast("Image selected!", 'success');
                        },
                    });
                }
            }
        } catch (e) {
            modifyCredits(cost, `Failed image generation for post #${post.id} - Refund`);
            addToast(`Image generation failed. Credits refunded. Error: ${e instanceof Error ? e.message : 'Unknown error'}`, 'error');
            if (count > 1) setImageSelectionOptions(prev => ({ ...prev, isOpen: false }));
        } finally {
            setLoadingStates(s => ({ ...s, image: null }));
        }
    };

    const handleImprovePost = async (post: Post, request: string): Promise<GeneratedPostContent | null> => {
        if (!checkCredits(COSTS.POST_IMPROVEMENT)) return null;
        setLoadingStates(s => ({...s, improve: true}));
        modifyCredits(-COSTS.POST_IMPROVEMENT, `Improved post #${post.id}`);
        try {
            const result = await improvePost(post, settings, request);
            if (!result) throw new Error("AI did not return a valid improvement.");
            addToast("AI suggestion ready!", 'success');
            return result;
        } catch (e) {
            modifyCredits(COSTS.POST_IMPROVEMENT, `Failed post improvement for #${post.id} - Refund`);
            addToast(`Post improvement failed. Credits refunded. Error: ${e instanceof Error ? e.message : 'Unknown error'}`, 'error');
            return null;
        } finally {
            setLoadingStates(s => ({...s, improve: false}));
        }
    };

    const handleAdaptPost = async (post: Post, targetPlatform: Platform): Promise<void> => {
        if (!checkCredits(COSTS.POST_ADAPTATION)) return;
        setLoadingStates(s => ({...s, adapt: true}));
        setSelectedPost(null); // Close the modal immediately
        modifyCredits(-COSTS.POST_ADAPTATION, `Adapted post #${post.id} for ${targetPlatform}`);
        try {
            const result = await adaptPostForPlatform(post, settings, targetPlatform);
            if (!result) throw new Error("AI did not return a valid adaptation.");

            await handleAddNewPost({
                platform: targetPlatform,
                caption: result.caption,
                hashtags: result.hashtags,
                image_prompt: result.image_prompt,
                likes: 0,
                comments: 0,
                date: new Date().toISOString(),
                scheduledDate: post.scheduledDate, // Keep the original scheduled date
                status: 'draft',
            });

        } catch (e) {
            modifyCredits(COSTS.POST_ADAPTATION, `Failed post adaptation for #${post.id} - Refund`);
            addToast(`Post adaptation failed. Credits refunded. Error: ${e instanceof Error ? e.message : 'Unknown error'}`, 'error');
        } finally {
            setLoadingStates(s => ({...s, adapt: false}));
        }
    };

    return (
        <AppContext.Provider value={{ handleLogout, handleExportData, handleImportData, handleResetApp, handleSubmitFeedback, handleStartTour, handleSkipOnboarding, handleCompleteOnboarding, ONBOARDING_STEPS, user }}>
            <UIContext.Provider value={{
                view, toasts, selectedPost, isSettingsOpen, isEarlyAdopterModalOpen, isFeedbackModalOpen, isPlanSummaryOpen,
                isAmbassadorModalOpen, isAskIhabModalOpen, isWelcomeModalOpen, imageSelectionOptions, isSubmittingFeedback, onboardingState, isAppLoading, initialSettingsTab,
                isProfileSetupNeeded: onboardingData.isProfileSetupNeeded,
                setView, addToast, setSelectedPost, setSettingsOpen, setFeedbackModalOpen, setPlanSummaryOpen, setAmbassadorModalOpen, setAskIhabModalOpen, setWelcomeModalOpen, setEarlyAdopterModalOpen, setOnboardingState, setToasts, setImageSelectionOptions, setInitialSettingsTab
            }}>
                <SettingsContext.Provider value={{ settings, loadingStates, handleSaveSettings, handleAdminAccess, handlePurchaseCredits, handleApplyPromoCode, checkCredits }}>
                    <PostsContext.Provider value={{ posts, imageGenAttemptedForPost, setImageGenAttemptedForPost, handleUpdatePost, handleDeletePost, handleAddNewPost, handleGenerateImageForPost, handleImprovePost, handleAdaptPost }}>
                        <PlanContext.Provider value={{ planMarkdown, completedWeeks, handleGeneratePlan, handleRefinePlan, handleGenerateWeeklyBatch, handleGenerateFirstMonth, handlePlanUpdate, handlePlanDurationChange }}>
                            <IntegrationsContext.Provider value={{ integrations, handleConnectPlatform, handleDisconnectPlatform }}>
                                {children}
                            </IntegrationsContext.Provider>
                        </PlanContext.Provider>
                    </PostsContext.Provider>
                </SettingsContext.Provider>
            </UIContext.Provider>
        </AppContext.Provider>
    );
};

// --- CUSTOM HOOKS ---
const useSettings = () => {
    const context = useContext(SettingsContext);
    if (!context) throw new Error('useSettings must be used within an AppProvider');
    return context;
};

const usePosts = () => {
    const context = useContext(PostsContext);
    if (!context) throw new Error('usePosts must be used within an AppProvider');
    return context;
};

const usePlan = () => {
    const context = useContext(PlanContext);
    if (!context) throw new Error('usePlan must be used within an AppProvider');
    return context;
};

const useUI = () => {
    const context = useContext(UIContext);
    if (!context) throw new Error('useUI must be used within an AppProvider');
    return context;
};

const useApp = () => {
    const context = useContext(AppContext);
    if (!context) throw new Error('useApp must be used within an AppProvider');
    return context;
};

const useIntegrations = () => {
    const context = useContext(IntegrationsContext);
    if (!context) throw new Error('useIntegrations must be used within an AppProvider');
    return context;
};

export {
    AppProvider,
    useSettings,
    usePosts,
    usePlan,
    useUI,
    useApp,
    useIntegrations,
    COSTS
};
