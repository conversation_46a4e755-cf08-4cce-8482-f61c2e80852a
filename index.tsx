/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import React, { useState, useEffect } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App';
import ErrorBoundary from './components/ErrorBoundary';
import LandingPage from './LandingPage';
import { AppProvider } from './contexts/AppContext';
import { supabase } from './services/supabaseClient';
import type { Session } from '@supabase/supabase-js';
import Loader from './components/ui/Loader';

const Main: React.FC = () => {
    const [session, setSession] = useState<Session | null>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
            setSession(session);
            setLoading(false);
        });

        // Check initial session
        supabase.auth.getSession().then(({ data: { session } }) => {
            setSession(session);
            setLoading(false);
        });

        return () => {
            subscription?.unsubscribe();
        };
    }, []);

    if (loading) {
        return (
             <div className="flex h-screen w-screen items-center justify-center bg-slate-900">
                <Loader size="lg" />
            </div>
        );
    }

    if (!session) {
        return <LandingPage />;
    }

    return (
        <AppProvider>
            <App />
        </AppProvider>
    );
};

const container = document.getElementById('root');
if (container) {
    const root = createRoot(container);
    root.render(
        <React.StrictMode>
            <ErrorBoundary>
                <Main />
            </ErrorBoundary>
        </React.StrictMode>
    );
} else {
    console.error('Root container could not be found in the DOM.');
}
