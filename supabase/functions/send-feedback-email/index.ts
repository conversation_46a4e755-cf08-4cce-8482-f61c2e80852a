/// <reference lib="deno.ns" />
// @deno-types="npm:@types/node-fetch"
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { getEmailTemplate } from '../_shared/email-template.ts';

const RESEND_API_KEY = Deno.env.get("RESEND_API_KEY");
const ADMIN_EMAIL = Deno.env.get("ADMIN_EMAIL");

function withCors(res: Response) {
  const headers = new Headers(res.headers);
  headers.set("Access-Control-Allow-Origin", "*");
  headers.set("Access-Control-Allow-Methods", "POST, OPTIONS");
  headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization");
  return new Response(res.body, { ...res, headers });
}

async function sendEmail(to: string, subject: string, html: string) {
  const res = await fetch("https://api.resend.com/emails", {
    method: "POST",
    headers: {
      "Authorization": `Bearer ${RESEND_API_KEY}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      from: `PulseCraft Feedback <<EMAIL>>`,
      to,
      subject,
      html,
    }),
  });
  if (!res.ok) {
    const error = await res.text();
    throw new Error(`Failed to send email: ${error}`);
  }
}

serve(async (req: Request) => {
  if (req.method === "OPTIONS") {
    // Handle CORS preflight
    return withCors(new Response(null, { status: 204 }));
  }
  try {
    const { userEmail, userName, feedbackType, message } = await req.json();
    if (!userEmail || !feedbackType || !message) {
      return withCors(new Response(JSON.stringify({ error: "Missing required fields." }), { status: 400 }));
    }

    // Email to admin
    const adminEmailContent = getEmailTemplate(
        `New Feedback: ${feedbackType}`,
        `<p><strong>Type:</strong> ${feedbackType}</p><p><strong>From:</strong> ${userName || userEmail}</p><p><strong>Message:</strong></p><p>${message}</p>`
    );
    await sendEmail(
      ADMIN_EMAIL!,
      `New Feedback: ${feedbackType}`,
      adminEmailContent
    );

    // Confirmation email to user
    const userEmailContent = getEmailTemplate(
        "Thank you for your feedback!",
        `<p>Hi${userName ? ' ' + userName : ''},</p><p>We received your feedback:</p><blockquote>${message}</blockquote><p>Thank you for helping us improve PulseCraft!</p>`
    );
    await sendEmail(
      userEmail,
      "Thank you for your feedback!",
      userEmailContent
    );

    return withCors(new Response(JSON.stringify({ success: true }), { status: 200 }));
  } catch (err) {
    return withCors(new Response(JSON.stringify({ error: err instanceof Error ? err.message : "Unknown error" }), { status: 500 }));
  }
});
