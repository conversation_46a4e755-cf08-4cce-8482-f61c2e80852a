/// <reference lib="deno.ns" />
// @deno-types="npm:@types/stripe"
import Stripe from "npm:stripe";

// Get Stripe secret key from environment variable
const STRIPE_SECRET_KEY = Deno.env.get("STRIPE_SECRET_KEY");
const stripe = new Stripe(STRIPE_SECRET_KEY!, { apiVersion: "2023-10-16" });

Deno.serve(async (req: Request) => {
  try {
    const { paymentMethodId, packName, userId } = await req.json();
    if (!paymentMethodId || !packName || !userId) {
      return new Response(JSON.stringify({ error: "Missing required fields." }), { status: 400 });
    }

    // Define your credit packs here
    const creditPacks: Record<string, { amount: number; credits: number }> = {
      "starter": { amount: 500, credits: 500 }, // $5.00
      "pro": { amount: 1500, credits: 2000 },   // $15.00
      "unlimited": { amount: 5000, credits: 999999 }, // $50.00
    };
    const pack = creditPacks[packName];
    if (!pack) {
      return new Response(JSON.stringify({ error: "Invalid pack name." }), { status: 400 });
    }

    // Create PaymentIntent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: pack.amount, // in cents
      currency: "usd",
      payment_method: paymentMethodId,
      confirm: true,
    });

    if (paymentIntent.status !== "succeeded") {
      return new Response(JSON.stringify({ error: "Payment not successful." }), { status: 402 });
    }

    // Update Supabase user profile (credits and creditHistory)
    const supabaseUrl = Deno.env.get("SUPABASE_URL");
    const supabaseKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
    const { createClient } = await import("npm:@supabase/supabase-js");
    const supabase = createClient(supabaseUrl!, supabaseKey!);

    // Fetch current profile
    const { data: profile, error: fetchError } = await supabase
      .from("profiles")
      .select("app_data")
      .eq("id", userId)
      .single();
    if (fetchError) {
      return new Response(JSON.stringify({ error: "Profile fetch failed." }), { status: 500 });
    }

    // Update credits and creditHistory
    const appData = profile.app_data || {};
    appData.credits = (appData.credits || 0) + pack.credits;
    appData.creditHistory = [
      ...(appData.creditHistory || []),
      {
        type: "purchase",
        amount: pack.credits,
        date: new Date().toISOString(),
        paymentIntentId: paymentIntent.id,
      },
    ];

    const { error: updateError } = await supabase
      .from("profiles")
      .update({ app_data: appData })
      .eq("id", userId);
    if (updateError) {
      return new Response(JSON.stringify({ error: "Profile update failed." }), { status: 500 });
    }

    return new Response(JSON.stringify({ success: true, credits: appData.credits }), { status: 200 });
  } catch (err) {
    return new Response(JSON.stringify({ error: err instanceof Error ? err.message : "Unknown error" }), { status: 500 });
  }
});
