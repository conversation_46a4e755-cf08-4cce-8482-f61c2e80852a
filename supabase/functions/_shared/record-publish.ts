import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

type PublishStatus = 'published' | 'failed';

interface RecordParams {
  post: any;
  platform: string;
  externalId: string | null;
  status: PublishStatus;
  meta?: any;
}

/**
 * Inserts a publishing log row to help track posting status per platform.
 * Table schema (create once in SQL editor if not present):
 *
 * create table if not exists publishing_logs (
 *   id uuid primary key default gen_random_uuid(),
 *   user_id uuid,
 *   post_id bigint,
 *   platform text,
 *   external_id text,
 *   status text,
 *   meta jsonb,
 *   created_at timestamptz default now()
 * );
 *
 * Note: This helper will no-op if insert fails; logs are best-effort.
 */
export async function recordPublishEvent(params: RecordParams) {
  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL')!,
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    );

    const payload = {
      user_id: params.post?.user_id ?? null,
      post_id: params.post?.id ?? null,
      platform: params.platform,
      external_id: params.externalId,
      status: params.status,
      meta: params.meta ?? null
    };

    const { error } = await supabase.from('publishing_logs').insert(payload);
    if (error) {
      console.warn('publishing_logs insert warning:', error.message);
    }
  } catch (e) {
    console.warn('recordPublishEvent failed:', (e as Error)?.message || e);
  }
}