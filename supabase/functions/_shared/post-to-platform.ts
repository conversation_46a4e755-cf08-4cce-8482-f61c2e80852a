import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { Post, Integration } from '../../../types.ts';

async function uploadLinkedInImage(post: Post, integration: Integration) {
  const supabase = createClient(Deno.env.get('SUPABASE_URL')!, Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!);
  const uploadFunctionName = 'upload-linkedin-image';
  const uploadEdgeFunctionUrl = `${Deno.env.get('SUPABASE_URL')}/functions/v1/${uploadFunctionName}`;
  
  const authorUrn = post.authorType === 'organization' ? integration.companyUrn : integration.authorUrn;

  const uploadResponse = await fetch(uploadEdgeFunctionUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`
    },
    body: JSON.stringify({
      accessToken: integration.accessToken,
      authorUrn: authorUrn,
      imageDataUrl: post.imgSrc,
    })
  });

  if (!uploadResponse.ok) {
    const errorBody = await uploadResponse.text();
    throw new Error(`Image upload failed: ${uploadResponse.status} ${errorBody}`);
  }
  
  const { assetUrn: returnedAssetUrn } = await uploadResponse.json();
  return returnedAssetUrn;
}

async function postToLinkedIn(post: Post, integration: Integration, assetUrn?: string) {
  const authorUrn = post.authorType === 'organization' ? integration.companyUrn : integration.authorUrn;
  const authorType = post.authorType || 'person';

  const shareContent: any = {
    shareCommentary: {
      text: [post.caption, post.hashtags].filter(Boolean).join('\n\n'),
    },
    shareMediaCategory: assetUrn ? 'IMAGE' : 'NONE',
  };

  if (assetUrn) {
    shareContent.media = [{
      status: 'READY',
      media: assetUrn,
    }];
  }

  const postBody = {
    author: `urn:li:${authorType}:${authorUrn}`,
    lifecycleState: 'PUBLISHED',
    specificContent: {
      'com.linkedin.ugc.ShareContent': shareContent,
    },
    visibility: {
      'com.linkedin.ugc.MemberNetworkVisibility': 'PUBLIC',
    },
  };

  const response = await fetch('https://api.linkedin.com/v2/ugcPosts', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${integration.accessToken}`,
      'Content-Type': 'application/json',
      'X-Restli-Protocol-Version': '2.0.0',
    },
    body: JSON.stringify(postBody),
  });

  if (!response.ok) {
    const errorBody = await response.text();
    throw new Error(`Failed to post to LinkedIn: ${response.status} ${errorBody}`);
  }

  const json = await response.json();
  // LinkedIn returns URN like 'urn:li:ugcPost:XXXX'
  const ugcPostUrn = json?.id || null;
  return { providerResponse: json, ugcPostUrn };
}

async function postToTwitter(post: Post, integration: Integration) {
    // Placeholder for Twitter posting logic
    console.log(`Posting to Twitter: ${post.caption}`);
    await Promise.resolve(); // Simulate async operation
    return { platform: 'x-twitter', externalId: `tweet-${Date.now()}`, meta: { status: 'success' } };
}

export async function postToPlatform(post: Post, integration: Integration) {
  if (post.platform === 'linkedin') {
    let assetUrn: string | undefined = undefined;
    if (post.imgSrc) {
      assetUrn = await uploadLinkedInImage(post, integration);
    }
    const result = await postToLinkedIn(post, integration, assetUrn);
    return { platform: 'linkedin', externalId: result?.ugcPostUrn || null, meta: result?.providerResponse || null };
  }
  if (post.platform === 'x-twitter') {
      return await postToTwitter(post, integration);
  }
  // Future platforms can be added here
  return { platform: post.platform, externalId: null, meta: null };
}
