/**
 * Fetch post stats for supported platforms.
 * Currently implements LinkedIn using the socialActions endpoint.
 *
 * LinkedIn reference:
 *   GET https://api.linkedin.com/v2/socialActions/{encoded URN}
 *   URN example for UGC: urn:li:ugcPost:<id>
 */
export interface PostLikeStats {
  likes: number;
  comments: number;
  shares: number;
}

function ensureLinkedInUgcUrn(externalId: string | null | undefined): string | null {
  if (!externalId) return null;
  if (externalId.startsWith('urn:li:ugcPost:')) return externalId;
  // If we only have the numeric id, prefix it
  if (/^\d+$/.test(externalId)) return `urn:li:ugcPost:${externalId}`;
  return externalId; // hope it is already a valid URN
}

export async function fetchPostStats(post: any, integration: any): Promise<PostLikeStats | null> {
  try {
    if (post.platform === 'linkedin') {
      const urn = ensureLinkedInUgcUrn(post.external_id);
      if (!urn) return null;
      const encodedUrn = encodeURIComponent(urn);
      const url = `https://api.linkedin.com/v2/socialActions/${encodedUrn}`;

      const res = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${integration.access_token || integration.accessToken}`,
          'X-Restli-Protocol-Version': '2.0.0'
        }
      });

      if (!res.ok) {
        // Swallow errors to avoid crashing scheduler; return null stats
        const body = await res.text();
        console.warn('LinkedIn stats fetch failed:', res.status, body);
        return null;
      }

      const json: any = await res.json();
      // LinkedIn fields shape example:
      // { "likesSummary":{ "totalLikes": 12 }, "commentsSummary":{ "count": 3 }, "sharesSummary": { "shareCount": 2 } }
      const likes = json?.likesSummary?.totalLikes ?? json?.reactionSummaries?.aggregateTotal ?? 0;
      const comments = json?.commentsSummary?.count ?? 0;
      const shares = json?.sharesSummary?.shareCount ?? 0;

      return { likes, comments, shares };
    }

    // Future providers (instagram/facebook/twitter) go here.
    return null;
  } catch (e) {
    console.warn('fetchPostStats error:', (e as Error)?.message || e);
    return null;
  }
}