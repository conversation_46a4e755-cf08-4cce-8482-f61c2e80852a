import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

export interface PostStats {
  likes: number;
  comments: number;
  shares: number;
}

export async function recordStatsRow(post: any, platform: string, stats: PostStats | null, meta: any = null) {
  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL')!,
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    );

    const payload = {
      user_id: post?.user_id ?? null,
      post_id: post?.id ?? null,
      platform,
      likes: stats?.likes ?? null,
      comments: stats?.comments ?? null,
      shares: stats?.shares ?? null,
      meta
    };

    const { error } = await supabase.from('post_metrics').insert(payload);
    if (error) {
      console.warn('post_metrics insert warning:', error.message);
    }
  } catch (e) {
    console.warn('recordStatsRow failed:', (e as Error)?.message || e);
  }
}

/**
SQL (run once in SQL editor if not present):

create table if not exists post_metrics (
  id uuid primary key default gen_random_uuid(),
  user_id uuid,
  post_id bigint,
  platform text,
  likes int,
  comments int,
  shares int,
  meta jsonb,
  created_at timestamptz default now()
);
*/