import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'
import { postToPlatform } from '../_shared/post-to-platform.ts'
import { recordPublishEvent } from '../_shared/record-publish.ts'
import { fetchPostStats } from '../_shared/fetch-stats.ts'
import { recordStatsRow } from '../_shared/record-stats.ts'

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL')!,
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    );

    console.log("Scheduler function invoked.");

    const now = new Date().toISOString();
    const { data: postsToPublish, error: postsError } = await supabase
      .from('posts')
      .select('*, integrations(*)')
      .eq('status', 'scheduled')
      .lte('scheduled_at', now);

    if (postsError) throw postsError;

    if (!postsToPublish || postsToPublish.length === 0) {
      console.log("No posts to publish.");
      return new Response(JSON.stringify({ success: true, message: "No posts to publish." }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      });
    }

    console.log(`Found ${postsToPublish.length} posts to publish.`);
    
    for (const post of postsToPublish) {
      const integration = post.integrations;
    
      if (!integration) {
        console.log(`No integration found for post #${post.id}`);
        await supabase.from('posts').update({ status: 'failed' }).eq('id', post.id);
        continue;
      }
    
      try {
        const result = await postToPlatform(post, integration);
        await supabase
          .from('posts')
          .update({ status: 'published', external_id: result?.externalId ?? null })
          .eq('id', post.id);

        // Record publish event (best-effort)
        await recordPublishEvent({
          post,
          platform: post.platform,
          externalId: result?.externalId ?? null,
          status: 'published',
          meta: result?.meta ?? null
        });

        // Immediately fetch initial stats (best-effort)
        const stats = await fetchPostStats({ ...post, external_id: result?.externalId ?? null }, integration);
        if (stats) {
          await recordStatsRow({ ...post, external_id: result?.externalId ?? null }, post.platform, stats, { source: 'scheduler-initial' });
        }

        console.log(`Successfully published post #${post.id}.`);
      } catch (e) {
        console.error(`Failed to publish post #${post.id}:`, (e as Error)?.message || e);
        await supabase.from('posts').update({ status: 'failed' }).eq('id', post.id);

        // Record failed event (best-effort)
        await recordPublishEvent({
          post,
          platform: post.platform,
          externalId: null,
          status: 'failed',
          meta: { error: String((e as Error)?.message || e) }
        });
      }
    }

    console.log("Scheduler function finished.");
    return new Response(JSON.stringify({ success: true, message: "Scheduler run completed." }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });

  } catch (error) {
    console.error("Error in scheduler function:", error.message);
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 400,
    });
  }
});