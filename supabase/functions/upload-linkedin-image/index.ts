import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { corsHeaders } from '../_shared/cors.ts'

interface UploadRequest {
  accessToken: string;
  authorUrn: string;
  imageDataUrl: string;
}

async function uploadImageToLinkedIn(req: UploadRequest) {
  const { accessToken, authorUrn, imageDataUrl } = req;

  // 1. Register the upload
  const registerUploadBody = {
    registerUploadRequest: {
      recipes: ["urn:li:digitalmediaRecipe:feedshare-image"],
      owner: `urn:li:person:${authorUrn}`,
      serviceRelationships: [{
        relationshipType: "OWNER",
        identifier: "urn:li:userGeneratedContent"
      }]
    }
  };

  const registerResponse = await fetch('https://api.linkedin.com/v2/assets?action=registerUpload', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(registerUploadBody),
  });

  if (!registerResponse.ok) {
    throw new Error(`Failed to register image upload: ${await registerResponse.text()}`);
  }
  const registerData = await registerResponse.json();
  const uploadUrl = registerData.value.uploadMechanism['com.linkedin.digitalmedia.uploading.MediaUploadHttpRequest'].uploadUrl;
  const assetUrn = registerData.value.asset;

  // 2. Upload the image binary
  const imageResponse = await fetch(imageDataUrl);
  const imageBlob = await imageResponse.blob();

  const uploadResponse = await fetch(uploadUrl, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': imageBlob.type,
    },
    body: imageBlob,
  });

  if (!uploadResponse.ok) {
    throw new Error(`Failed to upload image: ${await uploadResponse.text()}`);
  }

  // 3. Return the asset URN
  return { assetUrn };
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }
  try {
    const body: UploadRequest = await req.json();
    const result = await uploadImageToLinkedIn(body);
    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 400,
    });
  }
});
