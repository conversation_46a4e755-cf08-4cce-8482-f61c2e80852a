import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { corsHeaders } from '../_shared/cors.ts'

interface OAuthRequest {
  code: string;
  platform: string;
  redirectUri: string;
}

async function exchangeCodeForToken(req: OAuthRequest) {
  const { code, platform, redirectUri } = req;

  let tokenUrl = '';
  const params = new URLSearchParams();
  params.append('grant_type', 'authorization_code');
  params.append('code', code);
  params.append('redirect_uri', redirectUri);

  let client_id = '';
  let client_secret = '';

  switch (platform) {
    case 'linkedin':
      tokenUrl = 'https://www.linkedin.com/oauth/v2/accessToken';
      client_id = Deno.env.get('LINKEDIN_CLIENT_ID') || '';
      client_secret = Deno.env.get('LINKEDIN_CLIENT_SECRET') || '';
      break;
    case 'x-twitter':
      tokenUrl = 'https://api.twitter.com/2/oauth2/token';
      client_id = Deno.env.get('X_CLIENT_ID') || '';
      client_secret = Deno.env.get('X_CLIENT_SECRET') || '';
      params.append('code_verifier', 'challenge');
      break;
    case 'facebook':
    case 'instagram':
      tokenUrl = 'https://graph.facebook.com/v19.0/oauth/access_token';
      client_id = Deno.env.get('META_CLIENT_ID') || '';
      client_secret = Deno.env.get('META_CLIENT_SECRET') || '';
      break;
    default:
      throw new Error('Unsupported platform');
  }

  params.append('client_id', client_id);
  params.append('client_secret', client_secret);

  const response = await fetch(tokenUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: params,
  });

  if (!response.ok) {
    const errorBody = await response.text();
    throw new Error(`Token exchange failed: ${response.status} ${errorBody}`);
  }

  const tokenData = await response.json();
  let authorUrn = '';

  let companyUrn = '';

  // If it's LinkedIn, fetch the user's URN
  if (platform === 'linkedin') {
    const profileResponse = await fetch('https://api.linkedin.com/v2/userinfo', {
      headers: {
        Authorization: `Bearer ${tokenData.access_token}`,
      },
    });
    if (!profileResponse.ok) {
      throw new Error('Failed to fetch LinkedIn user profile.');
    }
    const profileData = await profileResponse.json();
    authorUrn = profileData.sub; // The 'sub' field contains the URN

    // If company scope is present, fetch company URN
    if (tokenData.scope && tokenData.scope.includes('w_organization_social')) {
      const companyResponse = await fetch('https://api.linkedin.com/v2/organizationalEntityAcls?q=roleAssignee', {
        headers: {
          'Authorization': `Bearer ${tokenData.access_token}`,
          'X-Restli-Protocol-Version': '2.0.0',
        },
      });
      if (companyResponse.ok) {
        const companyData = await companyResponse.json();
        if (companyData.elements && companyData.elements.length > 0) {
          companyUrn = companyData.elements[0].organizationalTarget;
        }
      }
    }
  }

  return {
    platform,
    accessToken: tokenData.access_token,
    refreshToken: tokenData.refresh_token,
    expiresAt: Date.now() + (tokenData.expires_in * 1000),
    scopes: tokenData.scope ? tokenData.scope.split(' ') : [],
    authorUrn,
    companyUrn,
  };
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const body: OAuthRequest = await req.json();
    if (!body.code || !body.platform || !body.redirectUri) {
        return new Response(JSON.stringify({ error: 'Missing required fields: code, platform, redirectUri' }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 400,
        });
    }
    const integrationData = await exchangeCodeForToken(body);
    return new Response(JSON.stringify(integrationData), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 400,
    });
  }
});
