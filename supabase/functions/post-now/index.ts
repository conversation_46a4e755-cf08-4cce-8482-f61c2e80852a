import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'
import { postToPlatform } from '../_shared/post-to-platform.ts'
import { recordPublishEvent } from '../_shared/record-publish.ts'

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { postId } = await req.json();
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL')!,
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    );

    const { data: post, error: postError } = await supabase
      .from('posts')
      .select('*')
      .eq('id', postId)
      .single();

    if (postError) throw postError;

    const { data: integration, error: integrationError } = await supabase
      .from('integrations')
      .select('*')
      .eq('user_id', post.user_id)
      .eq('platform', post.platform)
      .single();

    if (integrationError) throw integrationError;

    const result = await postToPlatform(post, integration);

    await supabase
      .from('posts')
      .update({ status: 'published', external_id: result?.externalId ?? null })
      .eq('id', postId);

    await recordPublishEvent({
      post,
      platform: post.platform,
      externalId: result?.externalId ?? null,
      status: 'published',
      meta: result?.meta ?? null
    });

    return new Response(JSON.stringify({ success: true, externalId: result?.externalId ?? null }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });

  } catch (error) {
    try {
      // best-effort logging if we have postId
      const supabase = createClient(
        Deno.env.get('SUPABASE_URL')!,
        Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
      );
      const { data: post } = await supabase.from('posts').select('*').eq('id', (await req.json()).postId).single();
      if (post) {
        await recordPublishEvent({
          post,
          platform: post.platform,
          externalId: null,
          status: 'failed',
          meta: { error: String(error?.message || error) }
        });
        await supabase.from('posts').update({ status: 'failed' }).eq('id', post.id);
      }
    } catch(_) {}

    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 400,
    });
  }
});
