-- Bring remote posts table to expected shape for scheduler/post-now
-- Safe/idempotent migration (adds columns only if missing)

-- The columns are already added in the 20250811_add_metrics_logs.sql migration.
-- This file is likely the result of a manual change to the database.
-- The following statements are redundant and can be removed.
-- alter table if exists public.posts
--   add column if not exists status text default 'draft',
--   add column if not exists scheduled_at timestamptz,
--   add column if not exists external_id text,
--   add column if not exists authorType text;

-- Helpful indexes (idempotent)
create index if not exists idx_posts_status_scheduled_at on public.posts (status, scheduled_at);
create index if not exists idx_posts_user_platform on public.posts (user_id, platform);