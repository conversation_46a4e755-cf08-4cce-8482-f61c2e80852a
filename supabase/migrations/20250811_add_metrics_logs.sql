-- Idempotent bootstrap for publishing + metrics
-- Run in Supabase SQL editor or via CLI migrations

-- Integrations table (per-user per-platform OAuth tokens)
create table if not exists public.integrations (
  user_id uuid not null,
  platform text not null,
  access_token text,
  refresh_token text,
  expires_at timestamptz,
  scopes text[],
  authorUrn text,
  companyUrn text,
  created_at timestamptz default now(),
  updated_at timestamptz default now(),
  primary key (user_id, platform)
);

-- Posts table (used by scheduler/post-now)
create table if not exists public.posts (
  id bigserial primary key,
  user_id uuid not null,
  platform text not null,
  caption text,
  hashtags text,
  imgSrc text,
  scheduled_at timestamptz,
  status text check (status in ('draft','scheduled','published','failed')) default 'draft',
  external_id text, -- provider post id/URN
  authorType text,  -- 'person' or 'organization' (for LinkedIn)
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);

-- Simple triggers to maintain updated_at
create or replace function public.set_updated_at()
returns trigger as $$
begin
  new.updated_at = now();
  return new;
end;
$$ language plpgsql;

do $$
begin
  if not exists (
    select 1 from pg_trigger where tgname = 'trg_posts_updated_at'
  ) then
    create trigger trg_posts_updated_at
    before update on public.posts
    for each row
    execute procedure public.set_updated_at();
  end if;
end $$;

do $$
begin
  if not exists (
    select 1 from pg_trigger where tgname = 'trg_integrations_updated_at'
  ) then
    create trigger trg_integrations_updated_at
    before update on public.integrations
    for each row
    execute procedure public.set_updated_at();
  end if;
end $$;

-- Publishing logs (audit of publish attempts)
create table if not exists public.publishing_logs (
  id uuid primary key default gen_random_uuid(),
  user_id uuid,
  post_id bigint,
  platform text,
  external_id text,
  status text,     -- 'published' | 'failed'
  meta jsonb,
  created_at timestamptz default now()
);

-- Metrics rollups (periodic snapshots)
create table if not exists public.post_metrics (
  id uuid primary key default gen_random_uuid(),
  user_id uuid,
  post_id bigint,
  platform text,
  likes int,
  comments int,
  shares int,
  meta jsonb,
  created_at timestamptz default now()
);

-- Ensure required columns exist on posts before creating indexes (idempotent)
alter table if exists public.posts
  add column if not exists status text,
  add column if not exists scheduled_at timestamptz;

-- Helpful indexes
create index if not exists idx_posts_status_scheduled_at on public.posts (status, scheduled_at);
create index if not exists idx_posts_user_platform on public.posts (user_id, platform);
create index if not exists idx_metrics_post on public.post_metrics (post_id, created_at desc);
create index if not exists idx_logs_post on public.publishing_logs (post_id, created_at desc);

-- Note: RLS policies can be added per your security model. Edge functions use service role.