-- Storage bucket and RLS policies for generated images

-- Create 'generated-images' bucket if it does not exist (public=true)
do $$
begin
  if not exists (select 1 from storage.buckets where id = 'generated-images') then
    perform storage.create_bucket('generated-images', true);
  end if;
end $$;

-- Recreate policies idempotently
drop policy if exists "generated_images_public_read" on storage.objects;
drop policy if exists "generated_images_auth_insert" on storage.objects;
drop policy if exists "generated_images_auth_update" on storage.objects;

-- Public read policy
create policy "generated_images_public_read"
on storage.objects for select
to public
using (bucket_id = 'generated-images');

-- Authenticated insert policy
create policy "generated_images_auth_insert"
on storage.objects for insert
to authenticated
with check (bucket_id = 'generated-images');

-- Authenticated update policy (needed when using upsert)
create policy "generated_images_auth_update"
on storage.objects for update
to authenticated
using (bucket_id = 'generated-images')
with check (bucket_id = 'generated-images');