import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config({ path: './.env' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase URL or Service Role Key is not defined in .env');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testSuccessfulPost() {
  console.log('Running end-to-end test for post-now function...');

  let testUser;
  let testPostId;

  try {
    // Step 1: Create a temporary test user with a post
    console.log('Creating a test user and post...');
    const { data: newUser, error: createError } = await supabase.auth.admin.createUser({
      email: `test-${Date.now()}@example.com`,
      password: 'password',
      user_metadata: {
        app_data: {
          posts: [
            {
              id: 1,
              platform: 'linkedin',
              caption: 'This is a test post from the automated test script.',
              hashtags: '#testing #automation #supabase',
              image_prompt: 'A robot writing code.',
              likes: 0,
              comments: 0,
              date: new Date().toISOString(),
              status: 'draft',
              authorType: 'person', // or 'organization' to test company posts
            },
          ],
          integrations: [
            {
              platform: 'linkedin',
              accessToken: process.env.LINKEDIN_ACCESS_TOKEN,
              authorUrn: process.env.LINKEDIN_AUTHOR_URN,
              companyUrn: process.env.LINKEDIN_COMPANY_URN,
            }
          ]
        },
      },
    });

    if (createError) {
      throw new Error(`Failed to create test user: ${createError.message}`);
    }
    testUser = newUser.user;
    testPostId = testUser.user_metadata.app_data.posts[0].id;
    console.log(`Test user created with ID: ${testUser.id}`);
    console.log(`Test post created with ID: ${testPostId}`);

    // Step 2: Invoke the post-now function
    console.log(`Invoking post-now function for post ID: ${testPostId}`);
    const { data, error: invokeError } = await supabase.functions.invoke('post-now', {
      body: { postId: testPostId },
    });

    if (invokeError) {
      console.error('Function returned an error:');
      console.error(invokeError.message);
    } else {
      console.log('Function returned a success response:');
      console.log(data);
    }
  } catch (err) {
    console.error('An unexpected error occurred during the test:');
    console.error(err.message);
  } finally {
    // Step 3: Clean up the test user
    if (testUser) {
      console.log(`Cleaning up test user with ID: ${testUser.id}`);
      const { error: deleteError } = await supabase.auth.admin.deleteUser(testUser.id);

      if (deleteError) {
        console.error(`Failed to clean up test user: ${deleteError.message}`);
      } else {
        console.log('Test user cleaned up successfully.');
      }
    }
  }
}

testSuccessfulPost();
