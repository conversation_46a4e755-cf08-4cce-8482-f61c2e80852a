export interface ContentTemplate {
  id: string;
  name: string;
  industry: string;
  description: string;
  targetAudience: string;
  brandVoice: string;
  visualStyle: string;
  platforms: string[];
  samplePosts: {
    platform: string;
    caption: string;
    hashtags: string;
    imagePrompt: string;
  }[];
  planOutline: string;
}

export const CONTENT_TEMPLATES: ContentTemplate[] = [
  {
    id: 'sustainable-fashion',
    name: 'Sustainable Fashion Brand',
    industry: 'Fashion & Apparel',
    description: 'Eco-friendly fashion startup targeting conscious consumers',
    targetAudience: 'Eco-conscious millennials and Gen Z who value sustainability, quality over quantity, and ethical consumption',
    brandVoice: 'Authentic, educational, inspiring, and empowering. Passionate about sustainability without being preachy.',
    visualStyle: 'Clean, natural, earth-toned aesthetic with authentic lifestyle photography. Minimalist design with sustainable materials focus.',
    platforms: ['instagram', 'x-twitter', 'linkedin'],
    samplePosts: [
      {
        platform: 'instagram',
        caption: '🌱 Every thread tells a story. What story does your wardrobe tell? Our organic cotton collection proves that sustainable can be stylish. Each piece is crafted with love for both you and the planet.',
        hashtags: '#SustainableFashion #EcoFriendly #OrganicCotton #EthicalFashion #SlowFashion',
        imagePrompt: 'Organic cotton clothing laid out on natural wood surface with plants, soft natural lighting'
      },
      {
        platform: 'x-twitter',
        caption: 'Fast fashion fact: The average garment is worn only 7 times before disposal. 😱\n\nOur pieces are designed to be loved for years, not seasons.\n\n#QualityOverQuantity #SustainableFashion',
        hashtags: '#QualityOverQuantity #SustainableFashion #SlowFashion',
        imagePrompt: 'Infographic showing fast fashion vs sustainable fashion statistics'
      }
    ],
    planOutline: 'Month 1: Brand foundation and sustainability education\nMonth 2: Product showcases and behind-the-scenes\nMonth 3: Community building and customer stories\nMonths 4-6: Seasonal collections and partnerships'
  },
  {
    id: 'saas-startup',
    name: 'B2B SaaS Startup',
    industry: 'Technology',
    description: 'Productivity software for small businesses and teams',
    targetAudience: 'Small business owners, team leaders, and productivity enthusiasts aged 28-45 who need efficient workflow solutions',
    brandVoice: 'Professional yet approachable, helpful, solution-focused. We simplify complex problems with clear, actionable advice.',
    visualStyle: 'Modern, clean interface screenshots, professional team photos, data visualizations, and productivity-focused imagery.',
    platforms: ['linkedin', 'x-twitter', 'instagram'],
    samplePosts: [
      {
        platform: 'linkedin',
        caption: 'Small business owners spend 40% of their time on administrative tasks instead of growing their business.\n\nOur latest feature automates invoice tracking, saving users 5+ hours per week.\n\nWhat administrative task would you most like to automate?',
        hashtags: '#SmallBusiness #Productivity #Automation #BusinessGrowth #Entrepreneurship',
        imagePrompt: 'Clean dashboard interface showing automated invoice tracking with time savings highlighted'
      },
      {
        platform: 'x-twitter',
        caption: 'Pro tip: The 2-minute rule 📝\n\nIf a task takes less than 2 minutes, do it now.\nIf it takes more, schedule it.\n\nOur app helps you sort tasks automatically based on estimated time.\n\n#ProductivityTip #TimeManagement',
        hashtags: '#ProductivityTip #TimeManagement #Efficiency',
        imagePrompt: 'Simple illustration of 2-minute rule with clock and task list'
      }
    ],
    planOutline: 'Month 1: Problem identification and solution positioning\nMonth 2: Feature highlights and use cases\nMonth 3: Customer success stories and testimonials\nMonths 4-6: Thought leadership and industry insights'
  },
  {
    id: 'fitness-coach',
    name: 'Personal Fitness Coach',
    industry: 'Health & Fitness',
    description: 'Online fitness coaching for busy professionals',
    targetAudience: 'Busy professionals aged 30-50 who want to get fit but struggle with time constraints and consistency',
    brandVoice: 'Motivational, supportive, realistic, and encouraging. We understand busy lifestyles and provide practical solutions.',
    visualStyle: 'High-energy workout photos, transformation stories, healthy meal prep, and motivational quotes with bold typography.',
    platforms: ['instagram', 'x-twitter', 'linkedin'],
    samplePosts: [
      {
        platform: 'instagram',
        caption: '💪 TRANSFORMATION TUESDAY: Meet Sarah, a busy mom of 2 who lost 25 lbs in 4 months with just 20-minute workouts!\n\n"I thought I needed hours at the gym. Turns out, I just needed the right plan." - Sarah\n\nConsistency > Perfection ✨',
        hashtags: '#TransformationTuesday #BusyMomFitness #20MinuteWorkouts #FitnessJourney #ConsistencyWins',
        imagePrompt: 'Before and after transformation photo with workout equipment and motivational text overlay'
      },
      {
        platform: 'x-twitter',
        caption: 'Busy schedule? Try this:\n\n🌅 5 min morning stretch\n🏃‍♀️ 15 min HIIT at lunch\n🧘‍♀️ 5 min evening meditation\n\n25 minutes total. Huge impact.\n\nSmall steps, big results. 💪\n\n#FitnessForBusyPeople #HIIT',
        hashtags: '#FitnessForBusyPeople #HIIT #QuickWorkouts',
        imagePrompt: 'Daily schedule infographic showing 25-minute fitness routine breakdown'
      }
    ],
    planOutline: 'Month 1: Fitness myths and quick workout solutions\nMonth 2: Nutrition tips for busy lifestyles\nMonth 3: Client transformations and success stories\nMonths 4-6: Advanced training and lifestyle integration'
  },
  {
    id: 'local-restaurant',
    name: 'Local Restaurant',
    industry: 'Food & Beverage',
    description: 'Family-owned restaurant focusing on fresh, local ingredients',
    targetAudience: 'Local food enthusiasts, families, and young professionals who appreciate quality dining and community connection',
    brandVoice: 'Warm, welcoming, community-focused, and passionate about food. We share the love behind every dish.',
    visualStyle: 'Mouth-watering food photography, behind-the-scenes kitchen shots, local ingredient sourcing, and happy customer moments.',
    platforms: ['instagram', 'facebook', 'x-twitter'],
    samplePosts: [
      {
        platform: 'instagram',
        caption: '🍅 FARM TO TABLE FRIDAY: Today\'s special features tomatoes from Johnson Family Farm, just 15 miles away! Chef Maria creates magic with these sun-ripened beauties.\n\nTaste the difference fresh makes. ✨\n\n#FarmToTable #LocalIngredients #FreshDaily',
        hashtags: '#FarmToTable #LocalIngredients #FreshDaily #ChefSpecial #CommunitySupported',
        imagePrompt: 'Beautiful plated dish with fresh tomatoes, chef in background, rustic restaurant setting'
      },
      {
        platform: 'x-twitter',
        caption: 'Sunday family dinner just hits different when it\'s made with love (and grandma\'s secret recipe) 👵❤️\n\nOur Sunday special: Traditional pot roast with all the fixings.\n\nBook your table! 📞\n\n#SundayDinner #FamilyRecipes',
        hashtags: '#SundayDinner #FamilyRecipes #ComfortFood',
        imagePrompt: 'Cozy family dinner scene with traditional pot roast and warm restaurant atmosphere'
      }
    ],
    planOutline: 'Month 1: Signature dishes and chef stories\nMonth 2: Local partnerships and ingredient sourcing\nMonth 3: Customer favorites and community events\nMonths 4-6: Seasonal menus and special occasions'
  }
];

export const getTemplateById = (id: string): ContentTemplate | undefined => {
  return CONTENT_TEMPLATES.find(template => template.id === id);
};

export const getTemplatesByIndustry = (industry: string): ContentTemplate[] => {
  return CONTENT_TEMPLATES.filter(template => template.industry === industry);
};

export const getAllIndustries = (): string[] => {
  return [...new Set(CONTENT_TEMPLATES.map(template => template.industry))];
};
