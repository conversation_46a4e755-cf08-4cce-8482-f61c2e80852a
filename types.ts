
/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/

export type Platform = 'instagram' | 'x-twitter' | 'linkedin' | 'facebook';
export type PostStatus = 'draft' | 'scheduled' | 'published' | 'failed';

export const ALL_PLATFORMS: Platform[] = ['instagram', 'x-twitter', 'linkedin', 'facebook'];

export type PlanDuration = '6m' | '1y' | '1.5y' | '2y';

export const ALL_DURATIONS: PlanDuration[] = ['6m', '1y', '1.5y', '2y'];

export interface Post {
    id: number;
    platform: Platform;
    caption: string;
    hashtags: string;
    image_prompt: string;
    likes: number;
    comments: number;
    date: string; // ISO string
    scheduledDate?: string; // ISO string
    imgSrc?: string; // data URL or remote URL
    weekId?: string;
    status: PostStatus;
    authorType?: 'person' | 'organization';
}

export interface CreditTransaction {
    timestamp: string;
    description: string;
    amount: number;
}

export interface Integration {
  platform: Platform;
  accessToken: string;
  refreshToken?: string;
  expiresAt?: number;
  scopes: string[];
  authorUrn?: string; // Unique user ID for platforms like LinkedIn
  companyUrn?: string;
}

export interface Settings {
    userId: string;
    credits: number;
    role: 'user' | 'admin';
    creditHistory: CreditTransaction[];
    showComingSoon: boolean;
    businessContext: string;
    brandName: string;
    bio: string;
    profileImageUrl: string;
    websiteUrl: string;
    generationLanguage: 'en' | 'fr' | 'es';
    characterDescription: string;
    styleDescription: string;
    strategyFramework: string;
    platforms: Platform[];
    planDuration: PlanDuration;
    integrations: Integration[];
    // UI preference for LinkedIn posting identity
    defaultLinkedInAuthorType?: 'person' | 'organization';
}

export interface BackupData {
    settings: Settings;
    posts: Omit<Post, 'imgSrc'>[];
    planMarkdown: string;
    completedWeeks: string[];
    onboardingData: {
        hasOnboarded: boolean;
        isProfileSetupNeeded: boolean;
        ambassadorModalShown: boolean;
        pioneerPromoRedeemed: boolean;
    };
}

export type View = 'dashboard' | 'planner' | 'strategy';
export type PlannerView = 'grid' | 'month' | 'week' | 'day' | 'agenda';

// For Strategy Plan
export interface PostIdea {
    concept: string;
    format: string;
}

export interface WeekData {
    strategy: string;
    audienceSegment: string;
    story: string;
    communityEngagement: string;
    postIdeas: PostIdea[];
}

export interface MonthData {
    [weekId: string]: WeekData;
}

export interface QuarterData {
    [monthName: string]: MonthData;
}

export interface YearData {
    [quarterName: string]: QuarterData;
}

export interface PlanHierarchy {
    [yearName: string]: YearData;
}

// For Gemini Service
export interface GeneratedPostContent {
    caption: string;
    hashtags: string;
    image_prompt: string;
}

export interface PostVariation extends GeneratedPostContent {
    platform: Platform;
}

// For UI
export interface ToastMessage {
    id: number;
    message: string;
    type: 'success' | 'error' | 'info';
    action?: {
        label: string;
        onClick: () => void;
    };
}

export interface CreditPack {
    name: string;
    credits: number;
    price: number;
    description: string;
    bestValue?: boolean;
}

export type SettingsTab = 'Profile' | 'AI Config' | 'Account' | 'Buy Credits' | 'Danger Zone';

export interface ImageVariationOptions {
    isOpen: boolean;
    isGenerating: boolean;
    images: string[];
    onSelect: (imgSrc: string) => void;
}

// For Onboarding Tour
export interface OnboardingStep {
    elementId: string;
    title: string;
    text: string;
    position?: 'top' | 'bottom' | 'left' | 'right';
    padding?: number;
    onCompleteAction?: {
        type: 'navigate';
        view: View;
    }
}

export interface OnboardingState {
    isActive: boolean;
    currentStep: number;
}

export interface OnboardingGuideProps {
    steps: OnboardingStep[];
    onboardingState: OnboardingState;
    setOnboardingState: React.Dispatch<React.SetStateAction<OnboardingState>>;
}

export interface HelperBotProps {
    step: OnboardingStep;
    onNext: () => void;
    onSkip: () => void;
    onPrev: () => void;
    isFirstStep: boolean;
    isLastStep: boolean;
    targetRect: DOMRect | null;
}

export interface AmbassadorModalProps {
    isOpen: boolean;
    onClose: () => void;
    onRedeem: () => void;
    settings: Settings;
}

export interface ChatMessage {
    role: 'user' | 'model';
    text: string;
}
