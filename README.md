# Run and deploy your AI Studio app

This contains everything you need to run your app locally.

## Run Locally

**Prerequisites:**  Node.js


1. Install dependencies:
   `npm install`
2. Set the `VITE_API_KEY` in [.env.local](.env.local) to your Gemini API key
3. Run the app:
   `npm run dev`

## Image generation resilience and configuration

This app now generates images on demand and fails gracefully with provider fallbacks and a final placeholder.

- Primary provider: Google Imagen via Gemini
- Fallbacks: Pollinations (free), Lexica (free search)
- Final fallback: local SVG placeholder, with credit refund if placeholder is used

Environment variables
- Add the following entries to your environment:
  - VITE_API_KEY: Gemini API key for Google Imagen
  - VITE_IMAGE_PROVIDER_ORDER: Comma-separated list among: google, pollinations, lexica. Example: google,pollinations,lexica

See env.example for an example configuration.

Behavior details
- If the primary provider fails, the app automatically tries the next providers in the configured order.
- If all providers fail, a neutral placeholder image is used and credits are refunded.
- Images are generated only on user action (button click). No scroll-triggered generation occurs.

Implementation overview
- Image generation with fallbacks and placeholder:
  - [generateImage()](services/geminiService.ts:30)
- Provider order via env:
  - [services/geminiService.ts](services/geminiService.ts:167)
- Credits refund on placeholder and UI selection flow:
  - [handleGenerateImageForPost()](contexts/AppContext.tsx:683)
- On-demand generation in grid (no IntersectionObserver, button-based):
  - [components/PostGrid.tsx](components/PostGrid.tsx:1)
