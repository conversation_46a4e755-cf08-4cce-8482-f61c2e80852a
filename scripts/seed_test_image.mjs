/*
  Seed a test image in Supabase Storage and attach its public URL to a post in the first profile.
  Purpose: Verify DB stores image URLs for posts (to confirm persistence across sessions).
  Usage:
    node scripts/seed_test_image.mjs
*/

import { readFileSync } from 'node:fs';
import { createClient } from '@supabase/supabase-js';

function parseDotEnv(raw) {
  const env = {};
  const lines = raw.split(/\r?\n/);
  for (const line of lines) {
    if (!line || line.trim().startsWith('#')) continue;
    const m = line.match(/^([A-Z0-9_]+)\s*=\s*(.*)$/i);
    if (!m) continue;
    let [, k, v] = m;
    v = v.trim();
    if ((v.startsWith('"') && v.endsWith('"')) || (v.startsWith("'") && v.endsWith("'"))) {
      v = v.slice(1, -1);
    }
    env[k] = v;
  }
  return env;
}

function isHttpUrl(s) {
  return typeof s === 'string' && /^https?:\/\//i.test(s);
}

async function ensureBucketPublic(supabase, bucket) {
  try {
    const { data, error } = await supabase.storage.getBucket(bucket);
    if (!error && data) return true;
  } catch (_) { /* ignore */ }

  // Create if missing
  const { data: created, error: createErr } = await supabase.storage.createBucket(bucket, {
    public: true,
    fileSizeLimit: '10MB'
  });
  if (createErr) {
    console.warn('createBucket result:', createErr.message);
    // Could already exist with different policy
  }
  return true;
}

async function uploadSeedSvg(supabase, bucket) {
  const svg = `
<svg xmlns='http://www.w3.org/2000/svg' width='1024' height='1024'>
  <defs>
    <linearGradient id='g' x1='0' x2='1' y1='0' y2='1'>
      <stop offset='0%' stop-color='#a78bfa'/>
      <stop offset='100%' stop-color='#f0abfc'/>
    </linearGradient>
  </defs>
  <rect width='1024' height='1024' fill='url(#g)'/>
  <g fill='#0f172a' font-family='system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif'>
    <text x='50%' y='48%' text-anchor='middle' font-size='48' font-weight='700'>Seed Test</text>
    <text x='50%' y='56%' text-anchor='middle' font-size='28'>PulseCraft</text>
  </g>
</svg>`.trim();

  const path = `seed-tests/${Date.now()}-${Math.random().toString(36).slice(2)}.svg`;
  const fileBody = Buffer.from(svg, 'utf8');

  const { error: uploadErr } = await supabase.storage.from(bucket).upload(path, fileBody, {
    contentType: 'image/svg+xml',
    upsert: true
  });
  if (uploadErr) {
    throw new Error(`Upload failed: ${uploadErr.message}`);
  }
  const { data } = supabase.storage.from(bucket).getPublicUrl(path);
  const publicUrl = data?.publicUrl || null;
  if (!publicUrl || !isHttpUrl(publicUrl)) {
    throw new Error('Failed to obtain public URL for uploaded image');
  }
  return publicUrl;
}

async function attachUrlToFirstProfile(supabase, publicUrl) {
  const { data: profiles, error } = await supabase.from('profiles').select('id, app_data').limit(1);
  if (error) throw new Error(`Load profiles failed: ${error.message}`);
  if (!profiles || profiles.length === 0) {
    throw new Error('No profiles found to seed.');
  }
  const profile = profiles[0];
  const appData = profile.app_data || {};
  const posts = Array.isArray(appData.posts) ? appData.posts : [];

  // Strategy: if there is a post without URL imgSrc, set it; else append a seed post
  let updated = false;
  for (const p of posts) {
    if (!isHttpUrl(p?.imgSrc)) {
      p.imgSrc = publicUrl;
      updated = true;
      break;
    }
  }
  if (!updated) {
    posts.push({
      id: 0,
      platform: 'instagram',
      caption: 'Seed test image',
      hashtags: '#seed #test',
      image_prompt: 'seed test',
      imgSrc: publicUrl,
      likes: 0,
      comments: 0,
      date: new Date().toISOString(),
      status: 'draft'
    });
  }

  appData.posts = posts;
  const { error: upErr } = await supabase.from('profiles').update({ app_data: appData }).eq('id', profile.id);
  if (upErr) throw new Error(`Update profile failed: ${upErr.message}`);

  return { profileId: profile.id, postCount: posts.length, url: publicUrl };
}

async function main() {
  let envText = '';
  try {
    envText = readFileSync('.env', 'utf8');
  } catch (e) {
    console.error('Could not read .env:', e?.message || e);
    process.exit(1);
  }
  const env = parseDotEnv(envText);

  const url = env.VITE_SUPABASE_URL;
  const serviceKey = env.SUPABASE_SERVICE_ROLE_KEY;
  const bucket = env.VITE_SUPABASE_BUCKET_IMAGES || 'generated-images';

  if (!url || !serviceKey) {
    console.error('Missing VITE_SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY in .env');
    process.exit(1);
  }

  const supabase = createClient(url, serviceKey);

  await ensureBucketPublic(supabase, bucket);
  const publicUrl = await uploadSeedSvg(supabase, bucket);
  const result = await attachUrlToFirstProfile(supabase, publicUrl);

  console.log('Seed complete:', result);
  console.log('Now run: node scripts/check_images.mjs');
}

main().catch(e => {
  console.error('Seed failed:', e?.message || e);
  process.exit(1);
});