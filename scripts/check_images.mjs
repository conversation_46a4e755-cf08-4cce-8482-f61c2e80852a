/* 
  Check Supabase storage and DB for persisted image URLs.
  - Reads .env directly (no dotenv dependency)
  - Uses SUPABASE_SERVICE_ROLE_KEY for server-side access
  - Prints:
    * Storage bucket sample (latest objects)
    * Profiles scanned, total posts, posts with URL imgSrc, and example rows
*/

import { readFileSync } from 'node:fs';
import { createClient } from '@supabase/supabase-js';

function parseDotEnv(raw) {
  const env = {};
  const lines = raw.split(/\r?\n/);
  for (const line of lines) {
    if (!line || line.trim().startsWith('#')) continue;
    const m = line.match(/^([A-Z0-9_]+)\s*=\s*(.*)$/i);
    if (!m) continue;
    let [, k, v] = m;
    v = v.trim();
    if ((v.startsWith('"') && v.endsWith('"')) || (v.startsWith("'") && v.endsWith("'"))) {
      v = v.slice(1, -1);
    }
    env[k] = v;
  }
  return env;
}

function isHttpUrl(s) {
  return typeof s === 'string' && /^https?:\/\//i.test(s);
}

async function main() {
  let envText = '';
  try {
    envText = readFileSync('.env', 'utf8');
  } catch (e) {
    console.error('Could not read .env:', e?.message || e);
    process.exit(1);
  }
  const env = parseDotEnv(envText);

  const url = env.VITE_SUPABASE_URL;
  const serviceKey = env.SUPABASE_SERVICE_ROLE_KEY;
  const bucket = env.VITE_SUPABASE_BUCKET_IMAGES || 'generated-images';

  if (!url || !serviceKey) {
    console.error('Missing VITE_SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY in .env');
    process.exit(1);
  }

  const supabase = createClient(url, serviceKey);

  // 1) Storage check
  try {
    const { data: rootList, error: rootErr } = await supabase.storage.from(bucket).list('', {
      limit: 100,
      sortBy: { column: 'created_at', order: 'desc' }
    });
    if (rootErr) {
      console.error('Storage list error:', rootErr.message);
    } else {
      const names = (rootList || []).map(f => f.name);
      console.log('Storage root objects (up to 100):', names.slice(0, 25));
      // If there are folders (names with '/'), list a couple for sampling
      const folders = names.filter(n => n && n.includes('/')).slice(0, 5);
      for (const folder of folders) {
        const prefix = folder.split('/')[0];
        const { data: subList } = await supabase.storage.from(bucket).list(prefix, {
          limit: 50,
          sortBy: { column: 'created_at', order: 'desc' }
        });
        console.log(`Storage objects under ${prefix}/:`, (subList || []).map(f => f.name).slice(0, 10));
      }
    }
  } catch (e) {
    console.error('Storage check failed:', e?.message || e);
  }

  // 2) Profiles scan for URL-based imgSrc
  let from = 0;
  const pageSize = 1000;
  let totalUrlPosts = 0;
  let totalPosts = 0;
  let profsChecked = 0;
  const examples = [];

  while (true) {
    const to = from + pageSize - 1;
    const { data, error, count } = await supabase
      .from('profiles')
      .select('id, app_data', { count: 'exact' })
      .range(from, to);

    if (error) {
      console.error('Profiles query error:', error.message);
      break;
    }
    if (!data || data.length === 0) break;

    for (const row of data) {
      const posts = (row?.app_data?.posts) || [];
      totalPosts += posts.length;
      const urlPosts = posts.filter(p => isHttpUrl(p?.imgSrc));
      totalUrlPosts += urlPosts.length;
      if (urlPosts.length && examples.length < 5) {
        examples.push({ id: row.id, exampleUrl: urlPosts[0].imgSrc });
      }
    }

    profsChecked += data.length;
    from += pageSize;
    if (typeof count === 'number' && from >= count) break;
  }

  console.log('Profiles scanned:', profsChecked);
  console.log('Total posts:', totalPosts);
  console.log('Posts with URL imgSrc:', totalUrlPosts);
  console.log('Example rows with URL imgSrc:', examples);

  if (totalUrlPosts === 0) {
    console.log('No URL-based images found yet. Generate/select an image after enabling VITE_SAVE_IMAGES_TO_SUPABASE=true to persist image URLs.');
  }
}

main().catch(e => {
  console.error('Unexpected error:', e?.message || e);
  process.exit(1);
});