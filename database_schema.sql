-- Create a new enum type for post status
CREATE TYPE post_status AS ENUM ('draft', 'scheduled', 'published', 'failed');

-- Alter the posts table to add the new status column
-- Note: This assumes you are storing posts in a table named 'posts'.
-- If your table has a different name, you will need to adjust this script.
-- This script is also not directly executable on a live Supabase project.
-- You will need to apply these changes through the Supabase dashboard's SQL editor.

-- Add the status column to your posts table, likely within the app_data JSONB of the profiles table.
-- The following is a conceptual representation.

-- Example of how you might alter your JSON structure:
-- Each post object within the 'posts' array in 'app_data' should be updated to include:
-- "status": "scheduled" (or "draft")
-- "authorType": "person" (or "organization")

-- To apply this, you would typically run an update script on your 'profiles' table.
-- For example (conceptual, do not run directly):
-- UPDATE profiles
-- SET app_data = jsonb_set(
--     app_data,
--     '{posts}',
--     (
--         SELECT jsonb_agg(
--             post || '{"status": "scheduled", "authorType": "person"}'::jsonb
--         )
--         FROM jsonb_array_elements(app_data->'posts') AS post
--     )
-- );

-- For new applications, it's better to design the JSON structure with the status and authorType fields from the start.
