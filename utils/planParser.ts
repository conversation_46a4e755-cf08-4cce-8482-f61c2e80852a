
import { PlanHierarchy, PostIdea } from '../types';

/**
 * Parses a markdown string representing a content plan into a structured PlanHierarchy object.
 * @param markdown The raw markdown string of the content plan.
 * @returns A nested object representing the plan's structure.
 */
export const parsePlanToHierarchy = (markdown: string): PlanHierarchy => {
    const hierarchy: PlanHierarchy = {};
    if (!markdown) return hierarchy;

    // Split the plan by years
    const yearSections = markdown.split(/(?=^##\s)/m).filter(Boolean);

    yearSections.forEach(yearSection => {
        const yearMatch = yearSection.match(/^##\s*(.*)/);
        if (!yearMatch) return;
        const yearName = yearMatch[1].trim();
        hierarchy[yearName] = {};

        // Split the year by quarters
        const quarterSections = yearSection.split(/(?=^###\s)/m).filter((s, i) => i > 0 || !s.startsWith('##'));

        quarterSections.forEach(quarterSection => {
            const quarterMatch = quarterSection.match(/^###\s*(.*)/);
            if (!quarterMatch) return;
            const quarterName = quarterMatch[1].trim();
            hierarchy[yearName][quarterName] = {};

            // Split the quarter by months
            const monthSections = quarterSection.split(/(?=^####\s)/m).filter((s, i) => i > 0 || !s.startsWith('###'));

            monthSections.forEach(monthSection => {
                const monthMatch = monthSection.match(/^####\s*(.*)/);
                if (!monthMatch) return;
                const monthName = monthMatch[1].trim();
                hierarchy[yearName][quarterName][monthName] = {};

                // Split the month by weeks
                const weekSections = monthSection.split(/(?=^#####\s)/m).filter((s, i) => i > 0 || !s.startsWith('####'));

                weekSections.forEach(weekSection => {
                    const weekMatch = weekSection.match(/^#####\s*(.*)/);
                    if (!weekMatch) return;
                    const weekId = weekMatch[1].trim();

                    const strategyMatch = weekSection.match(/\*\*Strategy:\*\*\s*(.*)/);
                    const audienceMatch = weekSection.match(/\*\*Audience Segment:\*\*\s*(.*)/);
                    const storyMatch = weekSection.match(/\*\*Story Idea:\*\*\s*(.*)/);
                    const engagementMatch = weekSection.match(/\*\*Community Engagement:\*\*\s*(.*)/);

                    const postIdeas: PostIdea[] = [];
                    // This regex finds pairs of Core Concept and Format
                    const postIdeasRegex = /\*\*Core Concept:\*\*\s*(.*?)\s*\n.*?\*\*Format:\*\*\s*(.*)/g;
                    let postIdeaMatch;
                    while ((postIdeaMatch = postIdeasRegex.exec(weekSection)) !== null) {
                        postIdeas.push({
                            concept: postIdeaMatch[1].trim(),
                            format: postIdeaMatch[2].trim(),
                        });
                    }

                    hierarchy[yearName][quarterName][monthName][weekId] = {
                        strategy: strategyMatch ? strategyMatch[1].trim() : '',
                        audienceSegment: audienceMatch ? audienceMatch[1].trim() : '',
                        story: storyMatch ? storyMatch[1].trim() : '',
                        communityEngagement: engagementMatch ? engagementMatch[1].trim() : '',
                        postIdeas: postIdeas,
                    };
                });
            });
        });
    });

    return hierarchy;
}