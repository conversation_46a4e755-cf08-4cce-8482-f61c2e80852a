export interface UserFriendlyError {
  title: string;
  message: string;
  action?: string;
  actionLabel?: string;
  type: 'error' | 'warning' | 'info';
}

export const ERROR_MESSAGES: Record<string, UserFriendlyError> = {
  // Authentication Errors
  'auth/user-not-found': {
    title: 'Account Not Found',
    message: 'We couldn\'t find an account with that email address. Would you like to create a new account?',
    action: 'signup',
    actionLabel: 'Create Account',
    type: 'error'
  },
  'auth/wrong-password': {
    title: 'Incorrect Password',
    message: 'The password you entered is incorrect. Please try again or reset your password.',
    action: 'reset-password',
    actionLabel: 'Reset Password',
    type: 'error'
  },
  'auth/email-already-in-use': {
    title: 'Email Already Registered',
    message: 'An account with this email already exists. Please sign in instead.',
    action: 'signin',
    actionLabel: 'Sign In',
    type: 'error'
  },
  'auth/weak-password': {
    title: 'Password Too Weak',
    message: 'Please choose a stronger password with at least 6 characters, including letters and numbers.',
    type: 'warning'
  },
  'auth/invalid-email': {
    title: 'Invalid Email',
    message: 'Please enter a valid email address.',
    type: 'error'
  },

  // Content Generation Errors
  'content/insufficient-credits': {
    title: 'Not Enough Credits',
    message: 'You need more credits to generate content. Each post generation costs 1 credit.',
    action: 'buy-credits',
    actionLabel: 'Buy Credits',
    type: 'warning'
  },
  'content/generation-failed': {
    title: 'Content Generation Failed',
    message: 'We couldn\'t generate your content right now. This might be due to high demand. Please try again in a few minutes.',
    action: 'retry',
    actionLabel: 'Try Again',
    type: 'error'
  },
  'content/invalid-prompt': {
    title: 'Invalid Content Request',
    message: 'Please provide more details about your brand and target audience to generate better content.',
    action: 'edit-settings',
    actionLabel: 'Update Brand Info',
    type: 'warning'
  },

  // Image Generation Errors
  'image/generation-failed': {
    title: 'Image Generation Failed',
    message: 'We couldn\'t create an image for your post. You can still use the text content and add your own image later.',
    action: 'continue',
    actionLabel: 'Continue Without Image',
    type: 'warning'
  },
  'image/inappropriate-content': {
    title: 'Image Request Rejected',
    message: 'Your image request was rejected by our content filters. Please try a different description.',
    type: 'warning'
  },

  // Network Errors
  'network/offline': {
    title: 'You\'re Offline',
    message: 'Please check your internet connection and try again.',
    action: 'retry',
    actionLabel: 'Retry',
    type: 'error'
  },
  'network/timeout': {
    title: 'Request Timed Out',
    message: 'The request is taking longer than expected. Please try again.',
    action: 'retry',
    actionLabel: 'Try Again',
    type: 'error'
  },
  'network/server-error': {
    title: 'Server Error',
    message: 'Something went wrong on our end. Our team has been notified and is working on a fix.',
    action: 'contact-support',
    actionLabel: 'Contact Support',
    type: 'error'
  },

  // Platform Integration Errors
  'platform/connection-failed': {
    title: 'Platform Connection Failed',
    message: 'We couldn\'t connect to your social media account. Please check your connection settings.',
    action: 'reconnect',
    actionLabel: 'Reconnect Account',
    type: 'error'
  },
  'platform/post-failed': {
    title: 'Post Failed to Publish',
    message: 'Your post couldn\'t be published to the platform. You can copy the content and post it manually.',
    action: 'copy-content',
    actionLabel: 'Copy Content',
    type: 'warning'
  },
  'platform/rate-limited': {
    title: 'Too Many Requests',
    message: 'You\'ve reached the platform\'s posting limit. Please wait a few minutes before trying again.',
    type: 'warning'
  },

  // Data/Storage Errors
  'storage/save-failed': {
    title: 'Save Failed',
    message: 'We couldn\'t save your changes. Please try again or contact support if the problem persists.',
    action: 'retry',
    actionLabel: 'Try Again',
    type: 'error'
  },
  'storage/load-failed': {
    title: 'Loading Failed',
    message: 'We couldn\'t load your data. Please refresh the page and try again.',
    action: 'refresh',
    actionLabel: 'Refresh Page',
    type: 'error'
  },

  // Generic Fallback
  'generic/unknown': {
    title: 'Something Went Wrong',
    message: 'An unexpected error occurred. Please try again or contact support if the problem continues.',
    action: 'contact-support',
    actionLabel: 'Contact Support',
    type: 'error'
  }
};

export const getErrorMessage = (errorCode: string, fallbackMessage?: string): UserFriendlyError => {
  const errorMessage = ERROR_MESSAGES[errorCode];
  
  if (errorMessage) {
    return errorMessage;
  }

  // Try to match partial error codes
  const partialMatch = Object.keys(ERROR_MESSAGES).find(key => 
    errorCode.includes(key.split('/')[1]) || key.includes(errorCode)
  );

  if (partialMatch) {
    return ERROR_MESSAGES[partialMatch];
  }

  // Return generic error with custom message if provided
  return {
    title: 'Something Went Wrong',
    message: fallbackMessage || 'An unexpected error occurred. Please try again.',
    action: 'retry',
    actionLabel: 'Try Again',
    type: 'error'
  };
};

export const getSuccessMessage = (action: string): { title: string; message: string } => {
  const SUCCESS_MESSAGES: Record<string, { title: string; message: string }> = {
    'content-generated': {
      title: 'Content Generated Successfully!',
      message: 'Your new posts are ready to review and customize.'
    },
    'post-published': {
      title: 'Post Published!',
      message: 'Your content has been successfully shared to your social media accounts.'
    },
    'settings-saved': {
      title: 'Settings Saved!',
      message: 'Your preferences have been updated successfully.'
    },
    'plan-generated': {
      title: 'Strategy Plan Created!',
      message: 'Your 6-month content strategy is ready. Let\'s start creating amazing content!'
    },
    'account-created': {
      title: 'Welcome to PulseCraft!',
      message: 'Your account has been created successfully. Let\'s build your content strategy!'
    },
    'credits-purchased': {
      title: 'Credits Added!',
      message: 'Your credits have been added to your account. You\'re ready to generate more content!'
    }
  };

  return SUCCESS_MESSAGES[action] || {
    title: 'Success!',
    message: 'Your action was completed successfully.'
  };
};

// Helper function to determine if an error is retryable
export const isRetryableError = (errorCode: string): boolean => {
  const retryableErrors = [
    'network/timeout',
    'network/server-error',
    'content/generation-failed',
    'storage/save-failed',
    'storage/load-failed'
  ];
  
  return retryableErrors.includes(errorCode) || errorCode.includes('timeout') || errorCode.includes('network');
};

// Helper function to get appropriate retry delay
export const getRetryDelay = (attemptNumber: number): number => {
  // Exponential backoff: 1s, 2s, 4s, 8s, max 30s
  return Math.min(1000 * Math.pow(2, attemptNumber - 1), 30000);
};
